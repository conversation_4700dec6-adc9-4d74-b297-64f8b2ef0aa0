import os
import re
import glob
import pygrib
import xarray as xr
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings("ignore")

print("环境设置完成，已导入所需库")

# 展示原始代码的主要配置参数和变量定义
print("=== ECMWF处理器的主要配置 ===")

# 地面变量类型展示
type1_vars = ['100u', '100v', '10fg3', '10fg6', '10u', '10v', '2d', '2t', 'cape', 'capes']
type5_vars = ['al', 'z']
upper_vars = ['v', 'u', 'd', 't', 'q', 'gh', 'pv', 'r', 'w']

print(f"地面变量类型1 (部分): {type1_vars}")
print(f"地面变量类型5: {type5_vars}")
print(f"高空变量: {upper_vars}")

# 预报时效展示
type1_hours = [6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 66, 72, 78, 84, 90, 96]
type5_hours = [0]

print(f"\n预报时效类型1: {type1_hours}")
print(f"预报时效类型5: {type5_hours}")

# 变量映射示例
var_mapping = {
    '100u': 'u100', '100v': 'v100', '10u': 'u10', '10v': 'v10',
    '2d': 'd2m', '2t': 't2m', 'msl': 'msl', 'sp': 'sp'
}
print(f"\n变量名映射示例: {var_mapping}")

# 文件命名规则对比分析
import pandas as pd

def analyze_filename_patterns():
    """分析两种数据格式的文件命名规则"""
    
    # ECMWF文件命名规则
    ecmwf_pattern = r'W_NAFP_C_ECMF_P_C1D_(\d{12})00_(\d{12})001_.*'
    ecmwf_example = "W_NAFP_C_ECMF_P_C1D_202401011200_202401021200_SURF.grib"
    
    # HRCLDAS文件命名规则  
    hrcldas_pattern = r'Z_NAFP_C_BABJ_(\d{14})_P_HRCLDAS_RT_BENN_0P01_HOR-(\w+)-(\d{10})\.GRB2'
    hrcldas_example = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    # 创建对比表
    comparison_data = {
        '特征': ['文件前缀', '数据中心', '时间格式', '变量组织', '文件扩展名', '分辨率信息'],
        'ECMWF': [
            'W_NAFP_C_ECMF',
            'ECMF (欧洲中期)',
            '参考时间+预报时间',
            '多变量单文件',
            '.grib',
            '无明确标识'
        ],
        'HRCLDAS': [
            'Z_NAFP_C_BABJ',
            'BABJ (北京)',
            '创建时间+数据时间',
            '单变量单文件',
            '.GRB2',
            '0P01 (0.01度)'
        ]
    }
    
    df = pd.DataFrame(comparison_data)
    print("=== 文件命名规则对比 ===")
    print(df.to_string(index=False))
    
    print(f"\n=== 示例文件名 ===")
    print(f"ECMWF:   {ecmwf_example}")
    print(f"HRCLDAS: {hrcldas_example}")
    
    return ecmwf_pattern, hrcldas_pattern

ecmwf_pat, hrcldas_pat = analyze_filename_patterns()

def parse_hrcldas_filename(filename):
    """
    解析HRCLDAS文件名，提取关键信息
    
    文件名格式: Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
    """
    pattern = r'Z_NAFP_C_BABJ_(\d{14})_P_HRCLDAS_RT_BENN_0P01_HOR-(\w+)-(\d{10})\.GRB2?'
    match = re.match(pattern, filename)
    
    if match:
        creation_time_str = match.group(1)  # 20240101000535
        variable = match.group(2)           # QAIR
        data_time_str = match.group(3)      # 2024010100
        
        try:
            creation_time = datetime.strptime(creation_time_str, "%Y%m%d%H%M%S")
            data_time = datetime.strptime(data_time_str, "%Y%m%d%H")
            
            return {
                'file_type': 'HRCLDAS',
                'creation_time': creation_time,
                'data_time': data_time,
                'variable': variable,
                'resolution': '0.01°',
                'domain': 'BENN',
                'format': 'horizontal',
                'original_filename': filename,
                'valid': True
            }
        except ValueError as e:
            print(f"时间解析错误: {e}")
            return {'valid': False, 'error': str(e)}
    else:
        return {'valid': False, 'error': '文件名格式不匹配'}

# 测试解析函数
test_filename = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
result = parse_hrcldas_filename(test_filename)

print("=== HRCLDAS文件名解析结果 ===")
if result['valid']:
    for key, value in result.items():
        print(f"{key:15}: {value}")
else:
    print(f"解析失败: {result['error']}")

def analyze_hrcldas_grib_data(filepath):
    """
    分析HRCLDAS GRIB文件的详细信息
    由于示例文件可能不存在，这里展示分析方法
    """
    
    def analyze_with_pygrib(filepath):
        """使用pygrib分析GRIB文件"""
        try:
            with pygrib.open(filepath) as grbs:
                print(f"\n=== PYGRIB分析结果 ===")
                print(f"文件: {Path(filepath).name}")
                
                messages = []
                for i, grb in enumerate(grbs, 1):
                    message_info = {
                        'message_num': i,
                        'short_name': getattr(grb, 'shortName', 'unknown'),
                        'name': getattr(grb, 'name', 'unknown'),
                        'units': getattr(grb, 'units', 'unknown'),
                        'level_type': getattr(grb, 'typeOfLevel', 'unknown'),
                        'level': getattr(grb, 'level', 0),
                        'valid_date': getattr(grb, 'validDate', None),
                        'valid_time': getattr(grb, 'validTime', None),
                    }
                    
                    # 获取网格信息
                    try:
                        lats, lons = grb.latlons()
                        message_info.update({
                            'grid_shape': lats.shape,
                            'lat_range': (lats.min(), lats.max()),
                            'lon_range': (lons.min(), lons.max()),
                            'data_range': (grb.values.min(), grb.values.max())
                        })
                    except:
                        message_info.update({
                            'grid_shape': 'N/A',
                            'lat_range': 'N/A',
                            'lon_range': 'N/A',
                            'data_range': 'N/A'
                        })
                    
                    messages.append(message_info)
                    
                    # 详细显示前3个消息
                    if i <= 3:
                        print(f"\n消息 {i}:")
                        for key, value in message_info.items():
                            if key != 'message_num':
                                print(f"  {key:12}: {value}")
                
                print(f"\n总消息数: {len(messages)}")
                return messages
        
        except Exception as e:
            print(f"PYGRIB分析失败: {str(e)}")
            return None
    
    def analyze_with_xarray(filepath):
        """使用xarray分析GRIB文件"""
        try:
            print(f"\n=== XARRAY分析结果 ===")
            ds = xr.open_dataset(filepath, engine='cfgrib')
            
            print(f"数据集维度: {dict(ds.dims)}")
            print(f"坐标变量: {list(ds.coords.keys())}")
            print(f"数据变量: {list(ds.data_vars.keys())}")
            
            # 变量详细信息
            for var_name, var in ds.data_vars.items():
                print(f"\n变量: {var_name}")
                print(f"  维度: {var.dims}")
                print(f"  形状: {var.shape}")
                print(f"  数据类型: {var.dtype}")
                print(f"  属性: {dict(var.attrs)}")
                
                # 数据统计
                try:
                    print(f"  数据范围: {var.min().values:.4f} ~ {var.max().values:.4f}")
                    print(f"  平均值: {var.mean().values:.4f}")
                except:
                    print("  无法计算统计信息")
            
            # 坐标信息
            for coord_name, coord in ds.coords.items():
                print(f"\n坐标: {coord_name}")
                print(f"  维度: {coord.dims}")
                print(f"  形状: {coord.shape}")
                if coord.size <= 10:
                    print(f"  值: {coord.values}")
                else:
                    print(f"  范围: {coord.min().values} ~ {coord.max().values}")
            
            return ds
        
        except Exception as e:
            print(f"XARRAY分析失败: {str(e)}")
            return None
    
    # 检查文件是否存在
    if not os.path.exists(filepath):
        print(f"文件不存在: {filepath}")
        print("这里展示分析方法，请替换为实际文件路径")
        return None
    
    # 解析文件名
    filename = Path(filepath).name
    file_info = parse_hrcldas_filename(filename)
    
    if file_info['valid']:
        print("=== 文件信息 ===")
        for key, value in file_info.items():
            if key != 'valid':
                print(f"{key:15}: {value}")
    
    # 分析文件内容
    pygrib_result = analyze_with_pygrib(filepath)
    xarray_result = analyze_with_xarray(filepath)
    
    return {
        'file_info': file_info,
        'pygrib_messages': pygrib_result,
        'xarray_dataset': xarray_result
    }

# 演示分析方法（使用示例文件名）
sample_file = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
print("=== HRCLDAS数据分析工具演示 ===")
print("注意: 请将示例文件替换为实际存在的文件路径")

# 如果在你的工作目录中有这个文件，取消下面这行的注释
# result = analyze_hrcldas_grib_data(sample_file)

class GribFileAnalyzer:
    """通用GRIB文件分析工具"""
    
    def __init__(self):
        self.supported_patterns = {
            'HRCLDAS': r'Z_NAFP_C_BABJ_(\d{14})_P_HRCLDAS_RT_BENN_0P01_HOR-(\w+)-(\d{10})\.GRB2?',
            'ECMWF': r'W_NAFP_C_ECMF.*\.grib?'
        }
    
    def identify_file_type(self, filename):
        """识别文件类型"""
        for file_type, pattern in self.supported_patterns.items():
            if re.match(pattern, filename):
                return file_type
        return 'UNKNOWN'
    
    def extract_basic_info(self, filepath):
        """提取文件基本信息"""
        if not os.path.exists(filepath):
            return {'error': 'File not found', 'filepath': filepath}
        
        filename = Path(filepath).name
        file_type = self.identify_file_type(filename)
        file_size = os.path.getsize(filepath) / (1024*1024)  # MB
        
        basic_info = {
            'filepath': filepath,
            'filename': filename,
            'file_type': file_type,
            'file_size_mb': round(file_size, 2),
            'modified_time': datetime.fromtimestamp(os.path.getmtime(filepath))
        }
        
        # 根据文件类型解析特定信息
        if file_type == 'HRCLDAS':
            parsed = parse_hrcldas_filename(filename)
            if parsed['valid']:
                basic_info.update(parsed)
        
        return basic_info
    
    def extract_grib_variables(self, filepath):
        """提取GRIB文件中的变量信息"""
        variables = []
        
        try:
            with pygrib.open(filepath) as grbs:
                for grb in grbs:
                    var_info = {
                        'short_name': getattr(grb, 'shortName', 'unknown'),
                        'name': getattr(grb, 'name', 'unknown'),
                        'units': getattr(grb, 'units', 'unknown'),
                        'level_type': getattr(grb, 'typeOfLevel', 'unknown'),
                        'level': getattr(grb, 'level', 0),
                        'valid_date': getattr(grb, 'validDate', None),
                        'valid_time': getattr(grb, 'validTime', None)
                    }
                    variables.append(var_info)
        except Exception as e:
            return {'error': str(e)}
        
        return variables
    
    def create_summary_report(self, file_paths):
        """创建多文件分析报告"""
        report = {
            'total_files': len(file_paths),
            'analyzed_files': 0,
            'file_types': {},
            'variables': set(),
            'errors': [],
            'details': []
        }
        
        for filepath in file_paths:
            try:
                # 基本信息
                basic_info = self.extract_basic_info(filepath)
                if 'error' in basic_info:
                    report['errors'].append(basic_info)
                    continue
                
                # 变量信息
                variables = self.extract_grib_variables(filepath)
                if isinstance(variables, dict) and 'error' in variables:
                    basic_info['grib_error'] = variables['error']
                    variables = []
                
                # 统计信息
                file_type = basic_info.get('file_type', 'UNKNOWN')
                report['file_types'][file_type] = report['file_types'].get(file_type, 0) + 1
                
                for var in variables:
                    report['variables'].add(var['short_name'])
                
                # 详细信息
                basic_info['variable_count'] = len(variables)
                basic_info['variables'] = variables
                report['details'].append(basic_info)
                report['analyzed_files'] += 1
                
            except Exception as e:
                report['errors'].append({'filepath': filepath, 'error': str(e)})
        
        report['variables'] = sorted(list(report['variables']))
        return report
    
    def print_summary(self, report):
        """打印分析报告摘要"""
        print("=== GRIB文件分析报告 ===")
        print(f"总文件数: {report['total_files']}")
        print(f"成功分析: {report['analyzed_files']}")
        print(f"错误数量: {len(report['errors'])}")
        
        print(f"\n文件类型分布:")
        for file_type, count in report['file_types'].items():
            print(f"  {file_type}: {count} 个文件")
        
        print(f"\n发现的变量 ({len(report['variables'])} 个):")
        for var in report['variables']:
            print(f"  {var}")
        
        if report['errors']:
            print(f"\n错误信息:")
            for error in report['errors'][:5]:  # 只显示前5个错误
                print(f"  {error}")

# 创建分析器实例
analyzer = GribFileAnalyzer()

# 演示用法
sample_files = [
    "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2",
    "Z_NAFP_C_BABJ_20240101010535_P_HRCLDAS_RT_BENN_0P01_HOR-TAIR-2024010101.GRB2"
]

print("=== 分析器工具演示 ===")
for sample_file in sample_files:
    basic_info = analyzer.extract_basic_info(sample_file)
    print(f"\n文件: {sample_file}")
    if 'error' not in basic_info:
        print(f"  类型: {basic_info.get('file_type', 'UNKNOWN')}")
        if 'variable' in basic_info:
            print(f"  变量: {basic_info['variable']}")
            print(f"  数据时间: {basic_info.get('data_time', 'N/A')}")
    else:
        print(f"  错误: {basic_info['error']}")

print("\n注意: 上述是演示代码，如需分析实际文件，请提供文件路径")

# 如果你有实际的HRCLDAS文件，可以使用以下代码进行分析
def analyze_actual_file():
    """分析实际的HRCLDAS文件"""
    
    # 请将下面的路径替换为你的实际文件路径
    actual_file = r"e:\liang\Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    if os.path.exists(actual_file):
        print(f"=== 分析实际文件: {Path(actual_file).name} ===")
        
        # 使用我们的分析器
        basic_info = analyzer.extract_basic_info(actual_file)
        variables = analyzer.extract_grib_variables(actual_file)
        
        print("基本信息:")
        for key, value in basic_info.items():
            if key != 'valid':
                print(f"  {key:15}: {value}")
        
        if isinstance(variables, list):
            print(f"\n变量信息 (共{len(variables)}个):")
            for i, var in enumerate(variables[:3], 1):  # 只显示前3个
                print(f"  变量{i}:")
                for k, v in var.items():
                    print(f"    {k:12}: {v}")
        else:
            print(f"变量提取错误: {variables.get('error', '未知错误')}")
        
        # 详细的GRIB分析
        print("\n" + "="*50)
        result = analyze_hrcldas_grib_data(actual_file)
        
        return result
    else:
        print(f"文件不存在: {actual_file}")
        print("请将路径替换为实际的HRCLDAS文件路径")
        return None

# 如果你有实际文件，取消下面这行的注释来执行分析
# result = analyze_actual_file()

print("="*60)
print("使用说明:")
print("1. 将actual_file变量设置为你的实际HRCLDAS文件路径")
print("2. 取消注释最后一行来执行分析")
print("3. 分析结果将包含文件的详细信息、变量属性、网格信息等")