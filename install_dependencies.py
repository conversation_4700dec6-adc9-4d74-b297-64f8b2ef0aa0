#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装GRIB处理所需的依赖库
"""

import subprocess
import sys
import importlib

def check_and_install_package(package_name, import_name=None):
    """检查并安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✓ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"✗ {package_name} 安装失败")
            return False

def install_conda_package(package_name):
    """使用conda安装包"""
    try:
        subprocess.check_call(["conda", "install", "-c", "conda-forge", package_name, "-y"])
        print(f"✓ {package_name} (conda) 安装成功")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print(f"✗ {package_name} (conda) 安装失败或conda不可用")
        return False

def main():
    """主安装函数"""
    print("开始检查和安装GRIB处理依赖库...")
    print("=" * 50)
    
    # 基础依赖
    basic_packages = [
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("xarray", "xarray"),
        ("netcdf4", "netCDF4"),
    ]
    
    print("\n1. 检查基础依赖...")
    for package, import_name in basic_packages:
        check_and_install_package(package, import_name)
    
    # GRIB相关依赖
    print("\n2. 检查GRIB处理依赖...")
    
    # pygrib
    if not check_and_install_package("pygrib", "pygrib"):
        print("   提示: pygrib安装失败，可能需要手动安装")
        print("   Windows用户可尝试: conda install -c conda-forge pygrib")
        print("   或下载预编译包: https://www.lfd.uci.edu/~gohlke/pythonlibs/")
    
    # cfgrib和eccodes
    print("\n3. 安装cfgrib和eccodes...")
    
    # 尝试用conda安装eccodes (推荐方式)
    eccodes_installed = install_conda_package("eccodes")
    
    if not eccodes_installed:
        print("   conda安装eccodes失败，尝试pip安装...")
        check_and_install_package("eccodes-python", "eccodes")
    
    # 安装cfgrib
    cfgrib_installed = check_and_install_package("cfgrib", "cfgrib")
    
    if not cfgrib_installed:
        print("   尝试用conda安装cfgrib...")
        install_conda_package("cfgrib")
    
    # 验证安装
    print("\n4. 验证安装...")
    test_imports = [
        ("numpy", "NumPy"),
        ("xarray", "xarray"),
        ("pygrib", "pygrib"),
        ("cfgrib", "cfgrib"),
        ("eccodes", "eccodes")
    ]
    
    success_count = 0
    for module, name in test_imports:
        try:
            importlib.import_module(module)
            print(f"✓ {name} 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"✗ {name} 导入失败: {e}")
    
    print(f"\n安装结果: {success_count}/{len(test_imports)} 个包可用")
    
    if success_count >= 3:  # numpy, xarray, pygrib至少要有
        print("✓ 基本功能可用，可以处理GRIB文件")
    else:
        print("✗ 关键依赖缺失，可能无法正常处理GRIB文件")
    
    print("\n安装建议:")
    print("1. 如果使用Anaconda/Miniconda，推荐用conda安装:")
    print("   conda install -c conda-forge pygrib cfgrib eccodes xarray netcdf4")
    print("2. 如果使用纯Python环境，可能需要手动安装一些系统依赖")
    print("3. Windows用户如遇到编译问题，可从以下网站下载预编译包:")
    print("   https://www.lfd.uci.edu/~gohlke/pythonlibs/")

if __name__ == "__main__":
    main()
