#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动GRIB处理器
"""

import os
import sys
from auto_grib_processor import AutoGRIBProcessor
from enhanced_batch_processor import EnhancedBatchProcessor

def test_auto_detection():
    """测试自动检测功能"""
    print("="*60)
    print("测试自动GRIB检测和转换功能")
    print("="*60)
    
    # 测试文件
    test_file = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        print("请将GRIB文件放在当前目录下进行测试")
        return False
    
    # 创建处理器
    processor = AutoGRIBProcessor("test_auto.log")
    
    print("\n1. 测试自动结构检测...")
    try:
        if processor.auto_detect_grib_structure(test_file):
            print("✓ 结构检测成功")
            
            # 显示检测结果
            print(f"   检测到变量: {list(processor.detected_variables.keys())}")
            print(f"   地面变量: {list(processor.surface_vars)}")
            print(f"   高空变量: {list(processor.upper_vars)}")
            if processor.pressure_levels:
                print(f"   气压层: {sorted(processor.pressure_levels, reverse=True)}")
        else:
            print("✗ 结构检测失败")
            return False
    except Exception as e:
        print(f"✗ 结构检测异常: {e}")
        return False
    
    print("\n2. 测试自动转换...")
    output_dir = "test_auto_output"
    try:
        if processor.auto_convert_grib_to_nc(test_file, output_dir):
            print("✓ 自动转换成功")
            
            # 检查输出文件
            if os.path.exists(output_dir):
                output_files = os.listdir(output_dir)
                print(f"   生成文件: {output_files}")
                
                # 简单验证NetCDF文件
                for nc_file in output_files:
                    if nc_file.endswith('.NC'):
                        nc_path = os.path.join(output_dir, nc_file)
                        file_size = os.path.getsize(nc_path) / 1024  # KB
                        print(f"   {nc_file}: {file_size:.1f} KB")
        else:
            print("✗ 自动转换失败")
            return False
    except Exception as e:
        print(f"✗ 自动转换异常: {e}")
        return False
    
    print("\n✓ 自动处理测试通过！")
    return True

def test_batch_processing():
    """测试批量处理功能"""
    print("\n" + "="*60)
    print("测试批量处理功能")
    print("="*60)
    
    # 查找当前目录下的GRIB文件
    import glob
    grib_files = glob.glob("*.GRB*") + glob.glob("*.grb*")
    
    if not grib_files:
        print("当前目录下没有GRIB文件，跳过批量处理测试")
        return True
    
    print(f"找到 {len(grib_files)} 个GRIB文件: {grib_files}")
    
    # 创建批量处理器
    batch_processor = EnhancedBatchProcessor("test_logs")
    
    try:
        # 处理当前目录
        output_dir = "test_batch_output"
        batch_processor.process_single_directory(".", output_dir)
        
        # 生成报告
        batch_processor.generate_summary_report(output_dir)
        
        print("✓ 批量处理测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 批量处理测试失败: {e}")
        return False

def demonstrate_features():
    """演示主要功能"""
    print("\n" + "="*60)
    print("自动GRIB处理器功能演示")
    print("="*60)
    
    print("""
主要功能:
1. 自动检测GRIB文件结构
   - 自动识别变量类型 (地面/高空)
   - 自动提取变量属性 (名称、单位、层次)
   - 自动分析时间和空间信息

2. 智能数据转换
   - 保留原有EC_2022_new.ipynb的转换逻辑
   - 自动生成SURF.NC (地面数据) 和 UPAR.NC (高空数据)
   - 保持原有的数据组织方式和属性

3. 多种读取方式
   - 优先使用cfgrib引擎 (如果可用)
   - 自动回退到pygrib备用方案
   - 智能处理不同GRIB版本

4. 批量处理能力
   - 单目录批量处理
   - 按时间范围处理 (类似原有逻辑)
   - 递归目录处理
   - 详细的处理日志和统计

5. 兼容性
   - 支持新格式: Z_NAFP_C_BABJ_*_HRCLDAS_*.GRB2
   - 支持旧格式: W_NAFP_C_ECMF_*_P_C1D*
   - 自动推断时间信息
   - 灵活的文件名解析

使用方式:
- 单文件: python auto_grib_processor.py
- 批量处理: python enhanced_batch_processor.py input_dir output_dir
- 时间范围: python enhanced_batch_processor.py input_dir output_dir --mode time_range --start_time 202401010000 --end_time 202401020000
""")

def main():
    """主测试函数"""
    print("自动GRIB处理器测试程序")
    
    # 检查依赖
    try:
        import numpy
        import xarray
        import pygrib
        print("✓ 基础依赖检查通过")
    except ImportError as e:
        print(f"✗ 依赖检查失败: {e}")
        print("请运行: python install_dependencies.py")
        return
    
    # 演示功能
    demonstrate_features()
    
    # 运行测试
    success_count = 0
    total_tests = 2
    
    if test_auto_detection():
        success_count += 1
    
    if test_batch_processing():
        success_count += 1
    
    # 测试结果
    print("\n" + "="*60)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！自动处理器可以正常使用")
        print("\n下一步:")
        print("1. 将你的GRIB文件放在合适的目录")
        print("2. 运行: python auto_grib_processor.py")
        print("3. 或使用批量处理: python enhanced_batch_processor.py input_dir output_dir")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    print("="*60)

if __name__ == "__main__":
    main()
