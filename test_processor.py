#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的GRIB2处理器
"""

import os
import sys
from new_grib2_processor import NewGRIB2Processor

def test_single_file():
    """测试单个文件处理"""
    file_path = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    if not os.path.exists(file_path):
        print(f"测试文件不存在: {file_path}")
        print("请确保文件在当前目录下")
        return False
    
    print("=" * 60)
    print("测试新GRIB2处理器")
    print("=" * 60)
    
    processor = NewGRIB2Processor()
    
    # 测试文件名解析
    print("\n1. 测试文件名解析...")
    try:
        file_info = processor.parse_filename(os.path.basename(file_path))
        print("✓ 文件名解析成功:")
        for key, value in file_info.items():
            if key != 'original_filename':
                print(f"   {key}: {value}")
    except Exception as e:
        print(f"✗ 文件名解析失败: {e}")
        return False
    
    # 测试文件分析
    print("\n2. 测试文件分析...")
    try:
        analysis_result = processor.analyze_single_file(file_path)
        if analysis_result and 'dataset' in analysis_result:
            print("✓ 文件分析成功，数据集已创建")
            ds = analysis_result['dataset']
            print(f"   数据变量: {list(ds.data_vars.keys())}")
            print(f"   维度: {dict(ds.dims)}")
            print(f"   坐标: {list(ds.coords.keys())}")
        else:
            print("✗ 文件分析失败，无法创建数据集")
            return False
    except Exception as e:
        print(f"✗ 文件分析异常: {e}")
        return False
    
    # 测试NetCDF转换
    print("\n3. 测试NetCDF转换...")
    output_dir = "test_output"
    try:
        success = processor.process_file_to_netcdf(file_path, output_dir)
        if success:
            print("✓ NetCDF转换成功")
            # 检查输出文件
            if os.path.exists(output_dir):
                output_files = os.listdir(output_dir)
                print(f"   输出文件: {output_files}")
        else:
            print("✗ NetCDF转换失败")
            return False
    except Exception as e:
        print(f"✗ NetCDF转换异常: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✓ 所有测试通过！处理器工作正常")
    print("=" * 60)
    return True

def test_dependencies():
    """测试依赖库"""
    print("检查依赖库...")
    
    dependencies = [
        ("os", "标准库"),
        ("re", "标准库"),
        ("numpy", "NumPy"),
        ("xarray", "xarray"),
        ("pygrib", "pygrib"),
    ]
    
    missing = []
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✓ {name}")
        except ImportError:
            print(f"✗ {name} - 缺失")
            missing.append(name)
    
    # 测试cfgrib
    try:
        import cfgrib
        print("✓ cfgrib")
    except ImportError:
        print("✗ cfgrib - 缺失 (将使用pygrib备用方案)")
        missing.append("cfgrib")
    
    if missing:
        print(f"\n缺失的依赖: {', '.join(missing)}")
        print("请运行 python install_dependencies.py 安装缺失的依赖")
        return False
    else:
        print("\n✓ 所有依赖都已安装")
        return True

def main():
    """主测试函数"""
    print("GRIB2处理器测试程序")
    print("=" * 60)
    
    # 检查依赖
    if not test_dependencies():
        print("\n请先安装缺失的依赖库")
        return
    
    # 测试处理器
    print("\n" + "=" * 60)
    if test_single_file():
        print("\n🎉 测试完成！处理器可以正常使用")
    else:
        print("\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
