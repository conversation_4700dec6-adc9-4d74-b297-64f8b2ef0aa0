#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动GRIB内容读取和NC转换处理器
保留EC_2022_new.ipynb的转换逻辑结构，但自动适配任何GRIB文件
"""

import os
import re
import glob
import xarray as xr
import numpy as np
import pygrib
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict
import warnings
import time
warnings.filterwarnings("ignore")

class AutoGRIBProcessor:
    """自动GRIB处理器 - 保留原有转换逻辑"""
    
    def __init__(self, log_file=None):
        self.log_file = log_file
        self.operation_start_time = {}
        
        # 自动检测的变量信息
        self.detected_variables = {}
        self.surface_vars = set()
        self.upper_vars = set()
        self.pressure_levels = set()
        
        # 层次类型分类 (保留原有逻辑)
        self.SURFACE_LEVEL_TYPES = [
            'surface', 'meanSea', 'heightAboveGround', 
            'heightAboveGroundLayer', 'cloudBase', 'cloudTop'
        ]
        self.UPPER_LEVEL_TYPES = ['isobaricInhPa']
        
        if log_file:
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("")
    
    def log_operation(self, operation_name, status_message):
        """记录操作结果 (保留原有日志逻辑)"""
        if operation_name not in self.operation_start_time:
            self.operation_start_time[operation_name] = time.time()
        
        elapsed = time.time() - self.operation_start_time[operation_name]
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
        message = f"{timestamp} {operation_name.ljust(12)}{elapsed:.2f}s\t{status_message}"
        
        print(message)
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message + "\n")
        
        if operation_name in self.operation_start_time:
            del self.operation_start_time[operation_name]
    
    def auto_detect_grib_structure(self, file_path):
        """自动检测GRIB文件结构"""
        print(f"\n=== 自动检测GRIB文件结构 ===")
        print(f"文件: {os.path.basename(file_path)}")
        
        self.operation_start_time["结构检测"] = time.time()
        
        try:
            with pygrib.open(file_path) as grbs:
                messages = list(grbs)
                print(f"GRIB消息总数: {len(messages)}")
                
                # 分析每个消息
                for i, grb in enumerate(messages):
                    try:
                        # 基本变量信息
                        var_name = grb.shortName if hasattr(grb, 'shortName') else f'var_{i}'
                        var_long_name = grb.name if hasattr(grb, 'name') else 'unknown'
                        units = grb.units if hasattr(grb, 'units') else 'unknown'
                        level_type = grb.typeOfLevel if hasattr(grb, 'typeOfLevel') else 'unknown'
                        level_value = grb.level if hasattr(grb, 'level') else 0
                        
                        # 时间信息
                        valid_date = grb.validDate if hasattr(grb, 'validDate') else None
                        valid_time = grb.validTime if hasattr(grb, 'validTime') else None
                        
                        # 存储变量信息
                        if var_name not in self.detected_variables:
                            self.detected_variables[var_name] = {
                                'long_name': var_long_name,
                                'units': units,
                                'level_types': set(),
                                'levels': set(),
                                'times': set(),
                                'count': 0
                            }
                        
                        var_info = self.detected_variables[var_name]
                        var_info['level_types'].add(level_type)
                        var_info['levels'].add(level_value)
                        var_info['count'] += 1
                        
                        if valid_date and valid_time:
                            var_info['times'].add(f"{valid_date}_{valid_time:04d}")
                        
                        # 分类变量 (保留原有分类逻辑)
                        if level_type in self.SURFACE_LEVEL_TYPES:
                            self.surface_vars.add(var_name)
                        elif level_type in self.UPPER_LEVEL_TYPES:
                            self.upper_vars.add(var_name)
                            self.pressure_levels.add(level_value)
                        
                    except Exception as e:
                        print(f"  处理消息 {i+1} 时出错: {e}")
                        continue
                
                # 输出检测结果
                print(f"\n检测到的变量 ({len(self.detected_variables)} 个):")
                for var_name, info in self.detected_variables.items():
                    var_type = "地面" if var_name in self.surface_vars else "高空" if var_name in self.upper_vars else "其他"
                    print(f"  {var_name} ({var_type}): {info['long_name']} [{info['units']}]")
                    print(f"    消息数: {info['count']}, 层次类型: {', '.join(info['level_types'])}")
                
                print(f"\n地面变量: {', '.join(sorted(self.surface_vars))}")
                print(f"高空变量: {', '.join(sorted(self.upper_vars))}")
                if self.pressure_levels:
                    print(f"气压层: {', '.join(map(str, sorted(self.pressure_levels, reverse=True)))}")
                
                status = f"检测到 {len(self.detected_variables)} 个变量 | 地面: {len(self.surface_vars)} | 高空: {len(self.upper_vars)}"
                self.log_operation("结构检测", status)
                
                return True
                
        except Exception as e:
            print(f"结构检测失败: {e}")
            self.log_operation("结构检测", f"失败: {str(e)}")
            return False
    
    def auto_convert_grib_to_nc(self, input_file, output_dir, reference_time=None):
        """自动转换GRIB为NetCDF (保留原有转换逻辑)"""
        print(f"\n=== 自动转换GRIB到NetCDF ===")
        
        # 如果没有提供参考时间，从文件名或内容中推断
        if reference_time is None:
            reference_time = self._infer_reference_time(input_file)
        
        self.operation_start_time["提取变量"] = time.time()
        
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # 读取数据 (保留原有读取逻辑)
            surface_datasets = []
            upper_datasets = []
            
            # 尝试使用cfgrib读取
            surface_ds, upper_ds = self._read_with_cfgrib(input_file)
            
            if surface_ds is None and upper_ds is None:
                # 备用方案：使用pygrib读取
                surface_ds, upper_ds = self._read_with_pygrib_advanced(input_file)
            
            if surface_ds is not None:
                surface_datasets.append(surface_ds)
            if upper_ds is not None:
                upper_datasets.append(upper_ds)
            
            # 记录提取的变量
            surface_var_names = []
            upper_var_names = []
            if surface_ds:
                surface_var_names = list(surface_ds.data_vars.keys())
            if upper_ds:
                upper_var_names = list(upper_ds.data_vars.keys())
            
            status = f"地面变量: {', '.join(surface_var_names)} | 高空变量: {', '.join(upper_var_names)}"
            self.log_operation("提取变量", status)
            
            # 保存结果 (保留原有保存逻辑)
            self.operation_start_time["转存NC"] = time.time()
            
            ref_dt = datetime.strptime(reference_time, "%Y%m%d%H%M") if isinstance(reference_time, str) else reference_time
            output_prefix = f"AUTO_GRIB_{ref_dt.strftime('%Y%m%d%H%M')}"
            
            saved_files = []
            
            # 保存地面数据
            if surface_datasets:
                surface_path = os.path.join(output_dir, f"{output_prefix}_SURF.NC")
                self._save_dataset(surface_datasets[0], surface_path, "地面数据", ref_dt)
                saved_files.append(f"SURF.NC -> {surface_path}")
            
            # 保存高空数据
            if upper_datasets:
                upper_path = os.path.join(output_dir, f"{output_prefix}_UPAR.NC")
                self._save_dataset(upper_datasets[0], upper_path, "高空数据", ref_dt)
                saved_files.append(f"UPAR.NC -> {upper_path}")
            
            if saved_files:
                status = " | ".join(saved_files)
                self.log_operation("转存NC", status)
                return True
            else:
                self.log_operation("转存NC", "没有数据可保存")
                return False
                
        except Exception as e:
            print(f"转换失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _read_with_cfgrib(self, file_path):
        """使用cfgrib读取数据 (保留原有读取逻辑)"""
        try:
            import cfgrib
            
            surface_ds = None
            upper_ds = None
            
            # 读取地面数据
            try:
                surface_ds = xr.open_dataset(
                    file_path,
                    engine="cfgrib",
                    backend_kwargs={
                        'filter_by_keys': {
                            'typeOfLevel': self.SURFACE_LEVEL_TYPES
                        }
                    }
                )
                if not surface_ds.data_vars:
                    surface_ds = None
            except Exception:
                pass
            
            # 读取高空数据
            try:
                upper_ds = xr.open_dataset(
                    file_path,
                    engine="cfgrib",
                    backend_kwargs={
                        'filter_by_keys': {
                            'typeOfLevel': 'isobaricInhPa'
                        }
                    }
                )
                if not upper_ds.data_vars:
                    upper_ds = None
            except Exception:
                pass
            
            return surface_ds, upper_ds
            
        except ImportError:
            print("cfgrib不可用，使用pygrib备用方案")
            return None, None
        except Exception as e:
            print(f"cfgrib读取失败: {e}")
            return None, None
    
    def _read_with_pygrib_advanced(self, file_path):
        """使用pygrib高级读取 (保留原有数据组织逻辑)"""
        try:
            with pygrib.open(file_path) as grbs:
                messages = list(grbs)
                
                if not messages:
                    return None, None
                
                # 获取网格信息
                first_msg = messages[0]
                lats, lons = first_msg.latlons()
                lat_1d = lats[:, 0]
                lon_1d = lons[0, :]
                
                # 分类数据
                surface_data = {}
                upper_data = defaultdict(dict)  # {level: {var: data}}
                
                for msg in messages:
                    var_name = msg.shortName
                    level_type = msg.typeOfLevel
                    level_value = msg.level
                    data = msg.values
                    
                    # 创建DataArray
                    da = xr.DataArray(
                        data,
                        dims=['latitude', 'longitude'],
                        coords={
                            'latitude': lat_1d,
                            'longitude': lon_1d
                        },
                        attrs={
                            'long_name': msg.name,
                            'units': msg.units,
                            'level_type': level_type,
                            'level': level_value
                        }
                    )
                    
                    # 分类存储 (保留原有分类逻辑)
                    if level_type in self.SURFACE_LEVEL_TYPES:
                        surface_data[var_name] = da
                    elif level_type == 'isobaricInhPa':
                        upper_data[level_value][var_name] = da
                
                # 创建Dataset
                surface_ds = None
                upper_ds = None
                
                if surface_data:
                    surface_ds = xr.Dataset(surface_data)
                    surface_ds = surface_ds.expand_dims({'level': [0]})
                
                if upper_data:
                    # 按气压层组织高空数据 (保留原有组织方式)
                    upper_datasets = []
                    for level in sorted(upper_data.keys(), reverse=True):
                        level_ds = xr.Dataset(upper_data[level])
                        level_ds = level_ds.expand_dims({'isobaricInhPa': [level]})
                        upper_datasets.append(level_ds)
                    
                    if upper_datasets:
                        upper_ds = xr.concat(upper_datasets, dim='isobaricInhPa')
                
                return surface_ds, upper_ds
                
        except Exception as e:
            print(f"pygrib高级读取失败: {e}")
            return None, None

    def _save_dataset(self, dataset, output_path, data_type, ref_time):
        """保存数据集 (保留原有保存逻辑)"""
        try:
            # 添加时间坐标
            if 'time' not in dataset.coords:
                dataset = dataset.expand_dims('time')
                dataset.coords['time'] = [ref_time]

            # 添加参考时间
            dataset.coords['reference_time'] = np.datetime64(ref_time, 'ns')

            # 添加全局属性 (保留原有属性逻辑)
            dataset.attrs.update({
                'modename': 'AUTO_GRIB_PROCESSOR',
                'description': f'Auto-processed {data_type}',
                'creation_time': datetime.now().isoformat(),
                'source_file': 'Auto-detected GRIB file'
            })

            # 添加变量元数据
            for var_name in dataset.data_vars:
                if var_name in self.detected_variables:
                    var_info = self.detected_variables[var_name]
                    dataset[var_name].attrs.update({
                        'long_name': var_info['long_name'],
                        'units': var_info['units']
                    })

            # 设置编码 (保留原有编码逻辑)
            encoding = {}
            for var in dataset.data_vars:
                encoding[var] = {
                    'zlib': True,
                    'complevel': 1,
                    'dtype': 'float32',
                    '_FillValue': None,
                    'chunksizes': self._get_chunksizes(dataset[var])
                }

            # 坐标编码
            encoding.update({
                'time': {'dtype': 'int64', '_FillValue': None},
                'reference_time': {'dtype': 'int64', '_FillValue': None}
            })

            # 保存文件
            dataset.to_netcdf(output_path, encoding=encoding)
            print(f"✓ 保存成功: {output_path}")

        except Exception as e:
            print(f"✗ 保存失败: {e}")
            raise

    def _get_chunksizes(self, var):
        """计算变量的合适chunk大小 (保留原有逻辑)"""
        chunks = []
        for dim in var.dims:
            size = len(var[dim])
            if dim == 'isobaricInhPa':
                chunks.append(min(4, size))
            elif dim == 'latitude':
                chunks.append(min(128, size))
            elif dim == 'longitude':
                chunks.append(min(256, size))
            elif dim in ['time', 'forecast_time']:
                chunks.append(1)
            else:
                chunks.append(size)
        return tuple(chunks)

    def _infer_reference_time(self, file_path):
        """从文件名或内容推断参考时间"""
        filename = os.path.basename(file_path)

        # 尝试从新格式文件名提取时间
        new_pattern = r'.*(\d{10})\.GRB2?$'
        match = re.search(new_pattern, filename)
        if match:
            time_str = match.group(1)  # YYYYMMDDHH
            try:
                return datetime.strptime(time_str, "%Y%m%d%H")
            except ValueError:
                pass

        # 尝试从旧格式文件名提取时间
        old_pattern = r'.*(\d{12}).*'
        match = re.search(old_pattern, filename)
        if match:
            time_str = match.group(1)  # YYYYMMDDHHMM
            try:
                return datetime.strptime(time_str, "%Y%m%d%H%M")
            except ValueError:
                pass

        # 默认使用当前时间
        return datetime.now().replace(minute=0, second=0, microsecond=0)

    def batch_process_directory(self, input_dir, output_dir, pattern="*.GRB*"):
        """批量处理目录 (保留原有批量处理逻辑)"""
        print(f"\n=== 批量处理GRIB文件 ===")
        print(f"输入目录: {input_dir}")
        print(f"输出目录: {output_dir}")
        print(f"文件模式: {pattern}")

        # 查找文件
        search_pattern = os.path.join(input_dir, pattern)
        files = glob.glob(search_pattern)

        if not files:
            print(f"未找到匹配的文件: {search_pattern}")
            return False

        print(f"找到 {len(files)} 个文件")

        success_count = 0
        for i, file_path in enumerate(files, 1):
            print(f"\n--- 处理文件 {i}/{len(files)} ---")
            print(f"文件: {os.path.basename(file_path)}")

            try:
                # 自动检测结构
                if self.auto_detect_grib_structure(file_path):
                    # 转换为NetCDF
                    if self.auto_convert_grib_to_nc(file_path, output_dir):
                        success_count += 1
                        print("✓ 处理成功")
                    else:
                        print("✗ 转换失败")
                else:
                    print("✗ 结构检测失败")

                # 清理检测结果，为下一个文件准备
                self._reset_detection()

            except Exception as e:
                print(f"✗ 处理异常: {e}")
                continue

        print(f"\n=== 批量处理完成 ===")
        print(f"成功处理: {success_count}/{len(files)} 个文件")
        return success_count > 0

    def _reset_detection(self):
        """重置检测结果"""
        self.detected_variables = {}
        self.surface_vars = set()
        self.upper_vars = set()
        self.pressure_levels = set()

def main():
    """主函数 - 使用示例"""
    # 创建处理器
    log_file = f"auto_grib_processing_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    processor = AutoGRIBProcessor(log_file)

    # 示例1: 处理单个文件
    input_file = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    output_dir = "auto_output"

    if os.path.exists(input_file):
        print("=== 单文件自动处理 ===")

        # 自动检测文件结构
        if processor.auto_detect_grib_structure(input_file):
            # 自动转换为NetCDF
            processor.auto_convert_grib_to_nc(input_file, output_dir)
    else:
        print(f"示例文件不存在: {input_file}")
        print("请将GRIB文件放在当前目录下，或修改文件路径")

    # 示例2: 批量处理目录
    # input_directory = "path/to/grib/files"
    # output_directory = "path/to/output"
    # processor.batch_process_directory(input_directory, output_directory)

if __name__ == "__main__":
    main()
