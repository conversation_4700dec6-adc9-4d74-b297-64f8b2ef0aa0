#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试地理坐标系信息
验证NetCDF文件中的坐标系统设置
"""

import os
import xarray as xr
import numpy as np
from EC_2022_adapted import ECMWFProcessorAdapted

def test_coordinate_attributes():
    """测试坐标属性设置"""
    print("=" * 60)
    print("测试地理坐标系属性")
    print("=" * 60)
    
    # 创建处理器
    processor = ECMWFProcessorAdapted()
    
    # 创建测试数据集
    lats = np.linspace(18.85, 28.85, 100)
    lons = np.linspace(101.95, 114.55, 126)
    
    # 创建测试DataArray
    test_data = xr.DataArray(
        np.random.random((100, 126)),
        dims=['latitude', 'longitude'],
        coords={
            'latitude': lats,
            'longitude': lons
        }
    )
    
    # 创建测试Dataset
    test_ds = xr.Dataset({'test_var': test_data})
    
    print("原始坐标属性:")
    print(f"  latitude attrs: {dict(test_ds.coords['latitude'].attrs)}")
    print(f"  longitude attrs: {dict(test_ds.coords['longitude'].attrs)}")
    
    # 添加坐标属性
    processor._add_coordinate_attributes(test_ds)
    
    print("\n添加属性后:")
    print(f"  latitude attrs: {dict(test_ds.coords['latitude'].attrs)}")
    print(f"  longitude attrs: {dict(test_ds.coords['longitude'].attrs)}")
    
    # 验证必要属性
    lat_attrs = test_ds.coords['latitude'].attrs
    lon_attrs = test_ds.coords['longitude'].attrs
    
    required_lat_attrs = ['units', 'long_name', 'standard_name', 'axis']
    required_lon_attrs = ['units', 'long_name', 'standard_name', 'axis']
    
    lat_ok = all(attr in lat_attrs for attr in required_lat_attrs)
    lon_ok = all(attr in lon_attrs for attr in required_lon_attrs)
    
    if lat_ok and lon_ok:
        print("✓ 坐标属性设置正确")
        return True
    else:
        print("✗ 坐标属性设置不完整")
        return False

def test_netcdf_output():
    """测试NetCDF输出中的坐标信息"""
    print("\n" + "=" * 60)
    print("测试NetCDF输出坐标信息")
    print("=" * 60)
    
    # 查找测试输出文件
    test_dirs = ["test_output_adapted", "output_adapted", "batch_output_adapted"]
    nc_files = []
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for file in os.listdir(test_dir):
                if file.endswith('.NC'):
                    nc_files.append(os.path.join(test_dir, file))
    
    if not nc_files:
        print("未找到测试输出的NetCDF文件")
        print("请先运行: python EC_2022_adapted.py 或 python test_adapted.py")
        return False
    
    print(f"找到 {len(nc_files)} 个NetCDF文件")
    
    for nc_file in nc_files[:2]:  # 只检查前2个文件
        print(f"\n检查文件: {os.path.basename(nc_file)}")
        
        try:
            with xr.open_dataset(nc_file) as ds:
                print(f"  维度: {dict(ds.dims)}")
                print(f"  坐标: {list(ds.coords.keys())}")
                
                # 检查坐标属性
                if 'latitude' in ds.coords:
                    lat_attrs = dict(ds.coords['latitude'].attrs)
                    print(f"  latitude属性: {lat_attrs}")
                    
                    # 验证关键属性
                    if 'units' in lat_attrs and lat_attrs['units'] == 'degrees_north':
                        print("    ✓ latitude单位正确")
                    else:
                        print("    ✗ latitude单位缺失或错误")
                
                if 'longitude' in ds.coords:
                    lon_attrs = dict(ds.coords['longitude'].attrs)
                    print(f"  longitude属性: {lon_attrs}")
                    
                    # 验证关键属性
                    if 'units' in lon_attrs and lon_attrs['units'] == 'degrees_east':
                        print("    ✓ longitude单位正确")
                    else:
                        print("    ✗ longitude单位缺失或错误")
                
                # 检查全局属性中的地理信息
                global_attrs = dict(ds.attrs)
                geo_attrs = [attr for attr in global_attrs.keys() if 'geospatial' in attr]
                if geo_attrs:
                    print(f"  地理范围属性: {geo_attrs}")
                    for attr in geo_attrs:
                        print(f"    {attr}: {global_attrs[attr]}")
                else:
                    print("  ✗ 缺少地理范围属性")
                
                # 检查CF约定
                if 'Conventions' in global_attrs:
                    print(f"  ✓ CF约定: {global_attrs['Conventions']}")
                else:
                    print("  ✗ 缺少CF约定")
                
        except Exception as e:
            print(f"  ✗ 读取文件失败: {e}")
            continue
    
    print("✓ NetCDF坐标信息检查完成")
    return True

def test_coordinate_ranges():
    """测试坐标范围"""
    print("\n" + "=" * 60)
    print("测试坐标范围")
    print("=" * 60)
    
    # 测试文件
    test_file = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return False
    
    try:
        # 使用pygrib读取坐标信息
        import pygrib
        
        with pygrib.open(test_file) as grbs:
            grb = grbs[1]  # 第一个消息
            
            print("GRIB文件坐标信息:")
            if hasattr(grb, 'latitudeOfFirstGridPointInDegrees'):
                print(f"  纬度范围: {grb.latitudeOfFirstGridPointInDegrees:.3f} 到 {grb.latitudeOfLastGridPointInDegrees:.3f}")
                print(f"  经度范围: {grb.longitudeOfFirstGridPointInDegrees:.3f} 到 {grb.longitudeOfLastGridPointInDegrees:.3f}")
            
            if hasattr(grb, 'Ni') and hasattr(grb, 'Nj'):
                print(f"  网格大小: {grb.Ni} x {grb.Nj}")
            
            # 获取完整的经纬度网格
            lats, lons = grb.latlons()
            print(f"  实际坐标范围:")
            print(f"    纬度: {lats.min():.3f} 到 {lats.max():.3f}")
            print(f"    经度: {lons.min():.3f} 到 {lons.max():.3f}")
            print(f"    网格形状: {lats.shape}")
        
        print("✓ 坐标范围检查完成")
        return True
        
    except Exception as e:
        print(f"✗ 坐标范围检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("地理坐标系测试程序")
    print("=" * 60)
    
    tests = [
        ("坐标属性测试", test_coordinate_attributes),
        ("NetCDF输出测试", test_netcdf_output),
        ("坐标范围测试", test_coordinate_ranges),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed >= 2:
        print("🎉 地理坐标系功能基本正常")
        print("\n现在NetCDF文件包含:")
        print("✓ 标准的经纬度坐标属性")
        print("✓ CF约定兼容的坐标系统")
        print("✓ 地理范围元数据")
        print("✓ 坐标轴标识")
    else:
        print("❌ 地理坐标系功能需要改进")

if __name__ == "__main__":
    main()
