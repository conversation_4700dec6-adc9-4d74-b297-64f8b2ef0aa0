#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GRIB2文件信息分析工具
用于分析新的GRIB2文件格式和内容，为数据处理提供参考
"""

import os
import sys
import pygrib
import xarray as xr
import numpy as np
from datetime import datetime, timedelta
import re
from pathlib import Path
import warnings
warnings.filterwarnings("ignore")

class GRIB2Analyzer:
    """GRIB2文件分析器"""
    
    def __init__(self):
        self.file_info = {}
        
    def analyze_file(self, file_path):
        """分析GRIB2文件的详细信息"""
        if not os.path.exists(file_path):
            print(f"错误: 文件 {file_path} 不存在")
            return None
            
        print(f"\n{'='*80}")
        print(f"分析文件: {os.path.basename(file_path)}")
        print(f"文件路径: {file_path}")
        print(f"{'='*80}")
        
        # 基本文件信息
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        print(f"文件大小: {file_size:.2f} MB")
        
        # 解析文件名信息
        self._parse_filename(file_path)
        
        # 使用pygrib分析GRIB内容
        self._analyze_with_pygrib(file_path)
        
        # 使用xarray分析数据结构
        self._analyze_with_xarray(file_path)
        
        return self.file_info
    
    def _parse_filename(self, file_path):
        """解析文件名获取基本信息"""
        filename = os.path.basename(file_path)
        print(f"\n--- 文件名解析 ---")
        print(f"原始文件名: {filename}")
        
        # 尝试解析新的文件名格式
        # Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
        pattern = r'Z_(\w+)_C_(\w+)_(\d{14})_P_(\w+)_(\w+)_(\w+)_(\w+)_(\w+)-(\w+)-(\d{10})\.(\w+)'
        match = re.match(pattern, filename)
        
        if match:
            print("识别为新格式文件:")
            groups = match.groups()
            info = {
                'data_source': groups[0],      # NAFP
                'center_code': groups[1],      # BABJ
                'creation_time': groups[2],    # 20240101000535
                'product_type': groups[3],     # HRCLDAS
                'data_type': groups[4],        # RT
                'region': groups[5],           # BENN
                'resolution': groups[6],       # 0P01
                'variable_info': groups[7],    # HOR
                'variable_name': groups[8],    # QAIR
                'data_time': groups[9],        # 2024010100
                'file_format': groups[10]      # GRB2
            }
            
            for key, value in info.items():
                print(f"  {key}: {value}")
                
            # 解析时间信息
            try:
                creation_dt = datetime.strptime(groups[2], "%Y%m%d%H%M%S")
                data_dt = datetime.strptime(groups[9], "%Y%m%d%H")
                print(f"  创建时间: {creation_dt.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"  数据时间: {data_dt.strftime('%Y-%m-%d %H:%M')}")
                
                self.file_info.update(info)
                self.file_info['creation_datetime'] = creation_dt
                self.file_info['data_datetime'] = data_dt
                
            except ValueError as e:
                print(f"  时间解析错误: {e}")
        else:
            print("未能识别文件名格式，可能是非标准格式")
    
    def _analyze_with_pygrib(self, file_path):
        """使用pygrib分析GRIB文件内容"""
        print(f"\n--- GRIB内容分析 (pygrib) ---")
        
        try:
            with pygrib.open(file_path) as grbs:
                messages = list(grbs)
                print(f"GRIB消息总数: {len(messages)}")
                
                # 分析每个消息
                variables = {}
                levels = set()
                times = set()
                
                for i, grb in enumerate(messages):
                    try:
                        # 基本信息
                        var_name = grb.shortName if hasattr(grb, 'shortName') else 'unknown'
                        var_long_name = grb.name if hasattr(grb, 'name') else 'unknown'
                        units = grb.units if hasattr(grb, 'units') else 'unknown'
                        level_type = grb.typeOfLevel if hasattr(grb, 'typeOfLevel') else 'unknown'
                        level_value = grb.level if hasattr(grb, 'level') else 'unknown'
                        
                        # 时间信息
                        if hasattr(grb, 'validDate') and hasattr(grb, 'validTime'):
                            valid_time = f"{grb.validDate} {grb.validTime:04d}"
                            times.add(valid_time)
                        
                        # 网格信息
                        if hasattr(grb, 'Ni') and hasattr(grb, 'Nj'):
                            grid_size = f"{grb.Ni}x{grb.Nj}"
                        else:
                            grid_size = "unknown"
                        
                        # 地理范围
                        if hasattr(grb, 'latitudeOfFirstGridPointInDegrees'):
                            lat_first = grb.latitudeOfFirstGridPointInDegrees
                            lon_first = grb.longitudeOfFirstGridPointInDegrees
                            lat_last = grb.latitudeOfLastGridPointInDegrees
                            lon_last = grb.longitudeOfLastGridPointInDegrees
                            geo_range = f"({lat_first:.3f},{lon_first:.3f}) to ({lat_last:.3f},{lon_last:.3f})"
                        else:
                            geo_range = "unknown"
                        
                        # 收集变量信息
                        if var_name not in variables:
                            variables[var_name] = {
                                'long_name': var_long_name,
                                'units': units,
                                'levels': set(),
                                'level_types': set(),
                                'grid_size': grid_size,
                                'geo_range': geo_range,
                                'count': 0
                            }
                        
                        variables[var_name]['levels'].add(level_value)
                        variables[var_name]['level_types'].add(level_type)
                        variables[var_name]['count'] += 1
                        levels.add(f"{level_type}:{level_value}")
                        
                    except Exception as e:
                        print(f"  处理消息 {i+1} 时出错: {e}")
                        continue
                
                # 输出变量汇总
                print(f"\n变量汇总 ({len(variables)} 个变量):")
                for var_name, info in variables.items():
                    print(f"  {var_name}:")
                    print(f"    长名称: {info['long_name']}")
                    print(f"    单位: {info['units']}")
                    print(f"    消息数: {info['count']}")
                    print(f"    网格大小: {info['grid_size']}")
                    print(f"    地理范围: {info['geo_range']}")
                    print(f"    层次类型: {', '.join(info['level_types'])}")
                    if len(info['levels']) <= 10:
                        print(f"    层次值: {', '.join(map(str, sorted(info['levels'])))}")
                    else:
                        sorted_levels = sorted(info['levels'])
                        print(f"    层次值: {', '.join(map(str, sorted_levels[:5]))} ... {', '.join(map(str, sorted_levels[-5:]))} (共{len(info['levels'])}个)")
                    print()
                
                # 输出时间信息
                if times:
                    print(f"时间信息 ({len(times)} 个时次):")
                    for time_str in sorted(times):
                        print(f"  {time_str}")
                
                self.file_info['variables'] = variables
                self.file_info['times'] = sorted(times)
                self.file_info['levels'] = sorted(levels)
                
        except Exception as e:
            print(f"pygrib分析失败: {e}")
    
    def _analyze_with_xarray(self, file_path):
        """使用xarray分析数据结构"""
        print(f"\n--- 数据结构分析 (xarray) ---")
        
        try:
            # 尝试不同的引擎和参数
            engines = ['cfgrib']
            
            for engine in engines:
                try:
                    print(f"尝试使用 {engine} 引擎...")
                    
                    if engine == 'cfgrib':
                        # 尝试读取所有数据
                        ds = xr.open_dataset(file_path, engine='cfgrib')
                        self._print_dataset_info(ds, "完整数据集")
                        
                        # 尝试按不同条件过滤
                        try:
                            # 按层次类型过滤
                            ds_surface = xr.open_dataset(
                                file_path, 
                                engine='cfgrib',
                                backend_kwargs={'filter_by_keys': {'typeOfLevel': 'surface'}}
                            )
                            self._print_dataset_info(ds_surface, "地面数据")
                        except:
                            pass
                        
                        try:
                            # 按等压面过滤
                            ds_pressure = xr.open_dataset(
                                file_path, 
                                engine='cfgrib',
                                backend_kwargs={'filter_by_keys': {'typeOfLevel': 'isobaricInhPa'}}
                            )
                            self._print_dataset_info(ds_pressure, "等压面数据")
                        except:
                            pass
                    
                    break  # 成功读取就退出循环
                    
                except Exception as e:
                    print(f"  {engine} 引擎失败: {e}")
                    continue
                    
        except Exception as e:
            print(f"xarray分析失败: {e}")
    
    def _print_dataset_info(self, ds, title):
        """打印数据集信息"""
        print(f"\n{title}:")
        print(f"  维度: {dict(ds.dims)}")
        print(f"  坐标: {list(ds.coords.keys())}")
        print(f"  数据变量: {list(ds.data_vars.keys())}")
        
        # 打印每个数据变量的详细信息
        for var_name in ds.data_vars:
            var = ds[var_name]
            print(f"    {var_name}: {var.dims} {var.shape}")
            if hasattr(var, 'attrs'):
                if 'long_name' in var.attrs:
                    print(f"      长名称: {var.attrs['long_name']}")
                if 'units' in var.attrs:
                    print(f"      单位: {var.attrs['units']}")
        
        # 打印坐标信息
        for coord_name in ds.coords:
            coord = ds.coords[coord_name]
            if coord.size > 0:
                if coord.size <= 10:
                    print(f"  {coord_name}: {coord.values}")
                else:
                    print(f"  {coord_name}: [{coord.values[0]} ... {coord.values[-1]}] (共{coord.size}个)")

def main():
    """主函数"""
    # 你的新文件路径
    file_path = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    # 如果文件不在当前目录，请修改为完整路径
    if not os.path.exists(file_path):
        print(f"请将文件 {file_path} 放在当前目录下，或修改脚本中的文件路径")
        print("当前目录:", os.getcwd())
        return
    
    analyzer = GRIB2Analyzer()
    result = analyzer.analyze_file(file_path)
    
    print(f"\n{'='*80}")
    print("分析完成!")
    print(f"{'='*80}")
    
    # 输出处理建议
    print("\n--- 处理建议 ---")
    if result and 'variables' in result:
        print("基于分析结果，如果要处理这个新格式的文件，需要:")
        print("1. 更新文件名解析逻辑以支持新的命名格式")
        print("2. 根据变量类型调整变量提取逻辑")
        print("3. 检查时间和坐标系统是否需要特殊处理")
        print("4. 验证数据的地理投影和网格信息")

if __name__ == "__main__":
    main()
