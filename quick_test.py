#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试EC_2022_adapted.py的语法和基本功能
"""

def test_import():
    """测试导入是否正常"""
    try:
        from EC_2022_adapted import ECMWFProcessorAdapted
        print("✓ 导入成功")
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_initialization():
    """测试初始化是否正常"""
    try:
        from EC_2022_adapted import ECMWFProcessorAdapted
        processor = ECMWFProcessorAdapted()
        print("✓ 初始化成功")
        
        # 检查关键属性
        print(f"  地面变量数: {len(processor.ALL_SURFACE_VARS)}")
        print(f"  高空变量数: {len(processor.UPPER_VARS)}")
        print(f"  变量映射数: {len(processor.GRIB_TO_NC_VAR_MAP)}")
        
        return True
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_filename_parsing():
    """测试文件名解析"""
    try:
        from EC_2022_adapted import ECMWFProcessorAdapted
        processor = ECMWFProcessorAdapted()
        
        # 测试文件名
        test_filename = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
        
        reference_time = processor._parse_new_format_time(test_filename)
        print(f"✓ 文件名解析成功: {reference_time}")
        
        return True
    except Exception as e:
        print(f"✗ 文件名解析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("EC_2022_adapted.py 快速测试")
    print("=" * 40)
    
    tests = [
        ("导入测试", test_import),
        ("初始化测试", test_initialization),
        ("文件名解析测试", test_filename_parsing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
            else:
                print(f"  {test_name} 失败")
        except Exception as e:
            print(f"  {test_name} 异常: {e}")
    
    print(f"\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础测试通过！代码语法正确")
        print("\n下一步:")
        print("1. 将GRIB2文件放在当前目录")
        print("2. 运行: python EC_2022_adapted.py")
        print("3. 或运行完整测试: python test_adapted.py")
    else:
        print("❌ 部分测试失败，请检查代码")

if __name__ == "__main__":
    main()
