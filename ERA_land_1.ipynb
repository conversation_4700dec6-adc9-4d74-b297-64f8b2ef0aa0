{"cells": [{"cell_type": "code", "execution_count": null, "id": "e6d86c5c-eb01-4d74-9bde-180194c7e439", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["分解 2010 年数据: 100%|██████████| 365/365 [1:50:38<00:00, 18.19s/it]\n", "合并变量:  14%|█▍        | 7/50 [21:51:36<134:17:01, 11242.36s/it]\n"]}], "source": ["############拆分单要素  再单要素按年合并##################\n", "import xarray as xr\n", "import os\n", "import time\n", "import glob\n", "from tqdm import tqdm\n", "import logging\n", "import gc\n", "import shutil\n", "\n", "def setup_logging(output_dir):\n", "    \"\"\"配置日志记录到文件\"\"\"\n", "    log_dir = os.path.join(output_dir, \"logs\")\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    log_path = os.path.join(log_dir, f\"era5_process_{time.strftime('%Y%m%d')}.log\")\n", "    \n", "    logging.basicConfig(\n", "        level=logging.INFO,\n", "        format=\"%(asctime)s [%(levelname)s] %(message)s\",\n", "        handlers=[logging.FileHandler(log_path, encoding='utf-8')]\n", "    )\n", "    logging.info(f\"初始化日志系统，日志文件保存在: {log_path}\")\n", "\n", "def validate_input_dir(year_dir):\n", "    \"\"\"验证输入目录是否存在且包含数据\"\"\"\n", "    if not os.path.exists(year_dir):\n", "        raise FileNotFoundError(f\"输入目录不存在: {year_dir}\")\n", "    \n", "    nc_files = glob.glob(os.path.join(year_dir, \"*.nc\"))\n", "    if not nc_files:\n", "        raise ValueError(f\"目录中未找到NC文件: {year_dir}\")\n", "    return nc_files\n", "\n", "def process_single_year(year_dir, output_base_dir, year):\n", "    \"\"\"\n", "    处理单年份数据的完整流程：\n", "    1. 按变量分解原始文件到临时目录\n", "    2. 合并各变量的临时文件为年度文件\n", "    3. 清理临时文件\n", "    \"\"\"\n", "    # 创建临时工作目录\n", "    temp_dir = os.path.join(output_base_dir, f\"TEMP_{year}\")\n", "    os.makedirs(temp_dir, exist_ok=True)\n", "    \n", "    try:\n", "        # 阶段1: 分解文件\n", "        nc_files = validate_input_dir(year_dir)\n", "        logging.info(f\"\\n{'='*60}\\n开始处理 {year} 年数据 (共 {len(nc_files)} 个文件)\")\n", "        \n", "        # 进度条包装器\n", "        def process_file(f):\n", "            try:\n", "                ds = xr.open_dataset(f)\n", "                date_str = os.path.basename(f).split('.')[1]  # 假设文件名格式: land.YYYYMMDD.nc\n", "                \n", "                for var in ds.data_vars:\n", "                    if var in ['longitude', 'latitude', 'time']:\n", "                        continue\n", "                        \n", "                    var_dir = os.path.join(temp_dir, var)\n", "                    os.makedirs(var_dir, exist_ok=True)\n", "                    output_path = os.path.join(var_dir, f\"era5_land.{var}.{date_str}.nc\")\n", "                    ds[[var]].to_netcdf(output_path)\n", "                \n", "                ds.close()\n", "                return True\n", "            except Exception as e:\n", "                logging.error(f\"文件处理失败 {os.path.basename(f)}: {str(e)}\")\n", "                return False\n", "\n", "        # 使用多线程处理会更快，但这里保持简单串行处理确保稳定性\n", "        with tqdm(nc_files, desc=f\"分解 {year} 年数据\") as pbar:\n", "            for f in pbar:\n", "                process_file(f)\n", "\n", "        # 阶段2: 合并变量\n", "        var_dirs = [d for d in glob.glob(os.path.join(temp_dir, \"*\")) if os.path.isdir(d)]\n", "        logging.info(f\"开始合并 {len(var_dirs)} 个变量\")\n", "        \n", "        for var_dir in tqdm(var_dirs, desc=\"合并变量\"):\n", "            var_name = os.path.basename(var_dir)\n", "            try:\n", "                # 检查文件一致性\n", "                sample = xr.open_dataset(glob.glob(f\"{var_dir}/*.nc\")[0])\n", "                expected_dims = sample.dims\n", "                sample.close()\n", "\n", "                # 合并处理 (优化内存使用)\n", "                ds = xr.open_mfdataset(\n", "                    f\"{var_dir}/*.nc\",\n", "                    combine=\"nested\",\n", "                    concat_dim=\"time\",\n", "                    parallel=False,\n", "                    chunks={\"time\": 30},  # 平衡内存和IO效率\n", "                    preprocess=lambda ds: ds.sortby('time')\n", "                )\n", "\n", "                # 保存结果\n", "                output_dir = os.path.join(output_base_dir, str(year))\n", "                os.makedirs(output_dir, exist_ok=True)\n", "                output_path = os.path.join(output_dir, f\"era5_land.{var_name}.{year}.nc\")\n", "                \n", "                # 分块写入以节省内存\n", "                encoding = {var_name: {'zlib': True, 'complevel': 1}}\n", "                ds.to_netcdf(output_path, encoding=encoding)\n", "                logging.info(f\"成功保存: {output_path}\")\n", "                \n", "                ds.close()\n", "            except Exception as e:\n", "                logging.error(f\"变量合并失败 {var_name}: {str(e)}\")\n", "            finally:\n", "                gc.collect()\n", "\n", "    finally:\n", "        # 阶段3: 确保清理临时文件\n", "        if os.path.exists(temp_dir):\n", "            shutil.rmtree(temp_dir, ignore_errors=True)\n", "            logging.info(f\"已清理临时目录: {temp_dir}\")\n", "\n", "def main():\n", "    # 配置参数\n", "    RAW_DATA_ROOT = \"/workspace/data/RDAT/ERA_LAND\"      # 原始数据根目录\n", "    OUTPUT_ROOT = \"/workspace/output/ERA5/ERA_LAND\"       # 输出根目录\n", "    START_YEAR = 2010                       # 起始年份\n", "    END_YEAR = 2015                         # 结束年份\n", "    \n", "    # 初始化日志（全局一次）\n", "    setup_logging(OUTPUT_ROOT)\n", "    logging.info(f\"开始处理 ERA5-Land 数据（{START_YEAR}-{END_YEAR}）\")\n", "\n", "    for year in range(START_YEAR, END_YEAR + 1):\n", "        input_dir = os.path.join(RAW_DATA_ROOT, str(year))\n", "        output_dir = os.path.join(OUTPUT_ROOT, str(year))\n", "        \n", "        try:\n", "            # 检查输入目录是否存在\n", "            if not os.path.exists(input_dir):\n", "                logging.warning(f\"跳过 {year} 年：输入目录不存在 {input_dir}\")\n", "                continue\n", "            \n", "            os.makedirs(output_dir, exist_ok=True)\n", "            logging.info(f\"\\n{'='*60}\\n处理年份: {year}\\n输入目录: {input_dir}\\n输出目录: {output_dir}\")\n", "            \n", "            # 处理当前年份\n", "            process_single_year(input_dir, OUTPUT_ROOT, year)\n", "            logging.info(f\"{year} 年处理完成！\")\n", "        \n", "        except Exception as e:\n", "            logging.error(f\"{year} 年处理失败: {str(e)}\", exc_info=True)\n", "            # 可选择继续处理下一年或终止\n", "            # raise  # 取消注释则遇到错误时停止\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": null, "id": "cdc2edf4-18ca-41e6-a946-34eecc7be076", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}