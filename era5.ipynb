{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2b10b687-c89c-4c9f-850c-c79fa2cfa376", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["===== CMA_GFS数据处理程序 =====\n", "start_time = \"20220329120000\"  # 开始时间\n", "end_time = \"20220429120000\"    # 结束时间\n", "base_dir = r\"/workspace/output/CMA_GFS\"  # 输入目录\n", "output_dir = r\"/workspace/output/CMA_GFS\"  # 输出目录\n", "日志文件: /workspace/output/CMA_GFS/processing_log20220329120000-20220429120000.txt\n", "========================================\n", "==== 处理时间点: 20220329120000 ====\n", "[2025-06-19 01:20:33] 读取文件        0.12s\t计划读取 61 个文件, 实际读取 47 个文件\n", "[2025-06-19 01:22:26] 提取变量      113.27s\t高空变量: 'ccl', 'clwmr', 'd', 'dpt', 'gh', 'grle', 'icmr', 'papt', 'q', 'r', 'rwmr', 'snmr', 't', 'u', 'v', 'vo', 'wz' | 地面变量: '10u', '10v', '2d', '2r', '2sh', '2t', 'acpcp', 'al', 'blh', 'bli', 'dlwrf_cs', 'hflux', 'i10fg', 'kx', 'ncpcp', 'nswrfcs', 'orog', 'pli', 'prmsl', 'sde', 'slhf', 'sp', 'ssr', 'str', 'sx', 't_s', 'tmax', 'tmin', 'ulwrf', 'uswrf', 'uswrf_cs', 'vis'\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 375\u001b[0m\n\u001b[1;32m    372\u001b[0m hour_str \u001b[38;5;241m=\u001b[39m ref_time[\u001b[38;5;241m8\u001b[39m:\u001b[38;5;241m10\u001b[39m]\n\u001b[1;32m    373\u001b[0m forecast_hours \u001b[38;5;241m=\u001b[39m get_forecast_hours(hour_str)\n\u001b[0;32m--> 375\u001b[0m success \u001b[38;5;241m=\u001b[39m process_forecast_files(ref_time, forecast_hours, base_dir, output_dir, logger)\n\u001b[1;32m    376\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m success:\n\u001b[1;32m    377\u001b[0m     processed_count \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "Cell \u001b[0;32mIn[1], line 341\u001b[0m, in \u001b[0;36mprocess_forecast_files\u001b[0;34m(reference_time, forecast_hours, base_dir, output_dir, logger)\u001b[0m\n\u001b[1;32m    339\u001b[0m upper_file \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mjoin(output_dir, \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCMA_GFS_GXDSJ_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mreference_time\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m_UPAR.nc\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    340\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 341\u001b[0m     save_upper_data(all_upper_data, \u001b[38;5;28msorted\u001b[39m(pressure_levels, reverse\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m), upper_file, sorted_times)\n\u001b[1;32m    342\u001b[0m     save_msg \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUPAR.NC -> \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mupper_file\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    343\u001b[0m     success \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "Cell \u001b[0;32mIn[1], line 238\u001b[0m, in \u001b[0;36msave_upper_data\u001b[0;34m(data_dict, levels, nc_file, times)\u001b[0m\n\u001b[1;32m    236\u001b[0m \u001b[38;5;66;03m# 压缩保存\u001b[39;00m\n\u001b[1;32m    237\u001b[0m encoding \u001b[38;5;241m=\u001b[39m {v: {\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mzlib\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28;01mTrue\u001b[39;00m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcomplevel\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m1\u001b[39m} \u001b[38;5;28;01mfor\u001b[39;00m v \u001b[38;5;129;01min\u001b[39;00m ds\u001b[38;5;241m.\u001b[39mvariables}\n\u001b[0;32m--> 238\u001b[0m ds\u001b[38;5;241m.\u001b[39mto_netcdf(nc_file, encoding\u001b[38;5;241m=\u001b[39mencoding, engine\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnetcdf4\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/xarray/core/dataset.py:2030\u001b[0m, in \u001b[0;36mDataset.to_netcdf\u001b[0;34m(self, path, mode, format, group, engine, encoding, unlimited_dims, compute, invalid_netcdf, auto_complex)\u001b[0m\n\u001b[1;32m   2027\u001b[0m     encoding \u001b[38;5;241m=\u001b[39m {}\n\u001b[1;32m   2028\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mxarray\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackends\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m to_netcdf\n\u001b[0;32m-> 2030\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m to_netcdf(  \u001b[38;5;66;03m# type: ignore[return-value]  # mypy cannot resolve the overloads:(\u001b[39;00m\n\u001b[1;32m   2031\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   2032\u001b[0m     path,\n\u001b[1;32m   2033\u001b[0m     mode\u001b[38;5;241m=\u001b[39mmode,\n\u001b[1;32m   2034\u001b[0m     \u001b[38;5;28mformat\u001b[39m\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mformat\u001b[39m,\n\u001b[1;32m   2035\u001b[0m     group\u001b[38;5;241m=\u001b[39mgroup,\n\u001b[1;32m   2036\u001b[0m     engine\u001b[38;5;241m=\u001b[39mengine,\n\u001b[1;32m   2037\u001b[0m     encoding\u001b[38;5;241m=\u001b[39mencoding,\n\u001b[1;32m   2038\u001b[0m     unlimited_dims\u001b[38;5;241m=\u001b[39munlimited_dims,\n\u001b[1;32m   2039\u001b[0m     compute\u001b[38;5;241m=\u001b[39mcompute,\n\u001b[1;32m   2040\u001b[0m     multifile\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m   2041\u001b[0m     invalid_netcdf\u001b[38;5;241m=\u001b[39minvalid_netcdf,\n\u001b[1;32m   2042\u001b[0m     auto_complex\u001b[38;5;241m=\u001b[39mauto_complex,\n\u001b[1;32m   2043\u001b[0m )\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/xarray/backends/api.py:1928\u001b[0m, in \u001b[0;36mto_netcdf\u001b[0;34m(dataset, path_or_file, mode, format, group, engine, encoding, unlimited_dims, compute, multifile, invalid_netcdf, auto_complex)\u001b[0m\n\u001b[1;32m   1923\u001b[0m \u001b[38;5;66;03m# TODO: figure out how to refactor this logic (here and in save_mfdataset)\u001b[39;00m\n\u001b[1;32m   1924\u001b[0m \u001b[38;5;66;03m# to avoid this mess of conditionals\u001b[39;00m\n\u001b[1;32m   1925\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1926\u001b[0m     \u001b[38;5;66;03m# TODO: allow this work (setting up the file for writing array data)\u001b[39;00m\n\u001b[1;32m   1927\u001b[0m     \u001b[38;5;66;03m# to be parallelized with dask\u001b[39;00m\n\u001b[0;32m-> 1928\u001b[0m     dump_to_store(\n\u001b[1;32m   1929\u001b[0m         dataset, store, writer, encoding\u001b[38;5;241m=\u001b[39mencoding, unlimited_dims\u001b[38;5;241m=\u001b[39munlimited_dims\n\u001b[1;32m   1930\u001b[0m     )\n\u001b[1;32m   1931\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m autoclose:\n\u001b[1;32m   1932\u001b[0m         store\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/xarray/backends/api.py:1975\u001b[0m, in \u001b[0;36mdump_to_store\u001b[0;34m(dataset, store, writer, encoder, encoding, unlimited_dims)\u001b[0m\n\u001b[1;32m   1972\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m encoder:\n\u001b[1;32m   1973\u001b[0m     variables, attrs \u001b[38;5;241m=\u001b[39m encoder(variables, attrs)\n\u001b[0;32m-> 1975\u001b[0m store\u001b[38;5;241m.\u001b[39mstore(variables, attrs, check_encoding, writer, unlimited_dims\u001b[38;5;241m=\u001b[39munlimited_dims)\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/xarray/backends/common.py:460\u001b[0m, in \u001b[0;36mAbstractWritableDataStore.store\u001b[0;34m(self, variables, attributes, check_encoding_set, writer, unlimited_dims)\u001b[0m\n\u001b[1;32m    458\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mset_attributes(attributes)\n\u001b[1;32m    459\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mset_dimensions(variables, unlimited_dims\u001b[38;5;241m=\u001b[39munlimited_dims)\n\u001b[0;32m--> 460\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mset_variables(\n\u001b[1;32m    461\u001b[0m     variables, check_encoding_set, writer, unlimited_dims\u001b[38;5;241m=\u001b[39munlimited_dims\n\u001b[1;32m    462\u001b[0m )\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/xarray/backends/common.py:502\u001b[0m, in \u001b[0;36mAbstractWritableDataStore.set_variables\u001b[0;34m(self, variables, check_encoding_set, writer, unlimited_dims)\u001b[0m\n\u001b[1;32m    497\u001b[0m check \u001b[38;5;241m=\u001b[39m vn \u001b[38;5;129;01min\u001b[39;00m check_encoding_set\n\u001b[1;32m    498\u001b[0m target, source \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprepare_variable(\n\u001b[1;32m    499\u001b[0m     name, v, check, unlimited_dims\u001b[38;5;241m=\u001b[39munlimited_dims\n\u001b[1;32m    500\u001b[0m )\n\u001b[0;32m--> 502\u001b[0m writer\u001b[38;5;241m.\u001b[39madd(source, target)\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/xarray/backends/common.py:344\u001b[0m, in \u001b[0;36mArrayWriter.add\u001b[0;34m(self, source, target, region)\u001b[0m\n\u001b[1;32m    342\u001b[0m     target[region] \u001b[38;5;241m=\u001b[39m source\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 344\u001b[0m     target[\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m] \u001b[38;5;241m=\u001b[39m source\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/xarray/backends/netCDF4_.py:80\u001b[0m, in \u001b[0;36mBaseNetCDF4Array.__setitem__\u001b[0;34m(self, key, value)\u001b[0m\n\u001b[1;32m     79\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__setitem__\u001b[39m(\u001b[38;5;28mself\u001b[39m, key, value):\n\u001b[0;32m---> 80\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdatastore\u001b[38;5;241m.\u001b[39mlock:\n\u001b[1;32m     81\u001b[0m         data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_array(needs_lock\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[1;32m     82\u001b[0m         data[key] \u001b[38;5;241m=\u001b[39m value\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/xarray/backends/locks.py:231\u001b[0m, in \u001b[0;36mCombinedLock.__exit__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m    228\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m lock \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlocks:\n\u001b[1;32m    229\u001b[0m         lock\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__enter__\u001b[39m()\n\u001b[0;32m--> 231\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__exit__\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs):\n\u001b[1;32m    232\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m lock \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlocks:\n\u001b[1;32m    233\u001b[0m         lock\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__exit__\u001b[39m(\u001b[38;5;241m*\u001b[39margs)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import pygrib \n", "import numpy as np\n", "import xarray as xr\n", "from datetime import datetime, timedelta\n", "from collections import defaultdict\n", "import os\n", "import time\n", "from multiprocessing import Pool\n", "import netCDF4\n", "# 定义需要提取的变量（全局常量）\n", "SURFACE_VARS = {\n", "    '100u', '100v', '10u', '10v', '200u', '200v', '2d', '2r', '2sh', '2t',\n", "    'acpcp', 'adlwrf_cs', 'al', 'asnow', 'auswrf_cs','blh','bli','cdcb', 'cdct',\n", "    'ceil', 'csuwf','dlwrf_cs','gust', 'hflux', 'hpbl','i10fg','kx', 'ncpcp','nswrfcs','orog', 'pli', 'prmsl',\n", "    'ptype', 'sde', 'sdlwrfcs','slhf','snswrfcs', 'sp', 'ssr', 'ssrc', 'str',  'sulwrf', 'suswrf', 'suswrfcs', 'sx', 'tmax', 'tmin',\n", "    'ulwrf', 'uswrf','uswrf_cs', 'vis', '30u', '30v', '50u', '50v', '70u', '70v',\n", "    '120u', '120v', '140u', '140v', '160u', '160v', '180u', '180v', 't_s'\n", "}\n", "\n", "UPPER_VARS = {\n", "    'ccl', 'clwmr', 'd', 'dpt', 'gh', 'grle', 'icmr', 'papt', 'q', 'r',\n", "    'rwmr', 'snmr', 't', 'u', 'v', 'vo', 'wz'\n", "}\n", "\n", "HEIGHT_VAR_MAP = {\n", "    30: ('30u', '30v', '30 metre U wind component', '30 metre V wind component'),\n", "    50: ('50u', '50v', '50 metre U wind component', '50 metre V wind component'),\n", "    70: ('70u', '70v', '70 metre U wind component', '70 metre V wind component'),\n", "    120: ('120u', '120v', '120 metre U wind component', '120 metre V wind component'),\n", "    140: ('140u', '140v', '140 metre U wind component', '140 metre V wind component'),\n", "    160: ('160u', '160v', '160 metre U wind component', '160 metre V wind component'),\n", "    180: ('180u', '180v', '180 metre U wind component', '180 metre V wind component')\n", "}\n", "\n", "SPECIAL_LONG_NAMES = {\n", "    't_s': 'Surface skin temperature',\n", "    't': 'Air temperature',\n", "    '100u': '100 metre U wind component',\n", "    '100v': '100 metre V wind component',\n", "    '200u': '200 metre U wind component',\n", "    '200v': '200 metre V wind component',\n", "    '10u': '10 metre U wind component',\n", "    '10v': '10 metre V wind component',\n", "    '2t': '2 metre temperature',\n", "    'csdlf': 'Downward clear-sky longwave radiation',  # dlwrf_cs的别名  'csdlf': 'dlwrf_cs', 晴空向下长波辐射\n", "    'ssrc': 'Net clear-sky shortwave radiation',       # nswrfcs的别名\n", "    'csuwf': 'Upward clear-sky shortwave radiation',   # uswrf_cs的别名\n", "    'i10fg': 'Instantaneous 10m wind gust',        # 'gust': 'i10fg',10米阵风\n", "    'bli': 'Best lifted index',\n", "    'hpbl': 'Planetary boundary layer height'# 边界层高度 'hpbl': 'blh'\n", "\n", "}\n", "\n", "class ProcessingLogger:\n", "    def __init__(self, output_dir, start_time, end_time):\n", "        \"\"\"初始化日志记录器\"\"\"\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        self.log_file = os.path.join(output_dir, f\"processing_log{start_time}-{end_time}.txt\")\n", "        header = f\"\"\"===== CMA_GFS数据处理程序 =====\n", "start_time = \"{start_time}\"  # 开始时间\n", "end_time = \"{end_time}\"    # 结束时间\n", "base_dir = r\"{output_dir}\"  # 输入目录\n", "output_dir = r\"{output_dir}\"  # 输出目录\n", "日志文件: {self.log_file}\n", "========================================\"\"\"\n", "        with open(self.log_file, 'w', encoding='utf-8') as f:\n", "            f.write(header + \"\\n\\n\")\n", "        print(header)\n", "    \n", "    def log(self, ref_time, operation, duration, details):\n", "        \"\"\"记录日志并打印到控制台\"\"\"\n", "        timestamp = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "        if operation == \"start\":\n", "            log_content = f\"\\n==== 处理时间点: {ref_time} ====\\n\"\n", "        elif operation == \"end\":\n", "            log_content = \"===== 处理完成 =====\\n\"\n", "        else:\n", "            log_content = f\"[{timestamp}] {operation.ljust(8)}{duration:>8.2f}s\\t{details}\\n\"\n", "        \n", "        with open(self.log_file, 'a', encoding='utf-8') as f:\n", "            f.write(log_content)\n", "        print(log_content.strip())\n", "\n", "def process_single_file(grib_file, ref_datetime, forecast_hour):\n", "    \"\"\"处理单个GRIB文件，提取目标变量\"\"\"\n", "    try:\n", "        grbs = pygrib.open(grib_file)\n", "        surface_data = []\n", "        upper_data = defaultdict(list)\n", "        extracted_vars = {'surface': set(), 'upper': set()}\n", "        lats, lons = None, None\n", "        \n", "        for grb in grbs:\n", "            original_name = getattr(grb, 'shortName', 'unknown')\n", "            level_type = getattr(grb, 'typeOfLevel', 'unknown')\n", "            level = getattr(grb, 'level', 0)\n", "            var_name = original_name\n", "            \n", "            # 处理特殊变量名\n", "            if original_name == 't' and level_type == 'surface':\n", "                var_name = 't_s'\n", "            \n", "            # 处理高度风分量变量\n", "            if original_name in ['u', 'v'] and level_type == 'heightAboveGround':\n", "                if level in HEIGHT_VAR_MAP:\n", "                    u_name, v_name, _, _ = HEIGHT_VAR_MAP[level]\n", "                    var_name = u_name if original_name == 'u' else v_name\n", "            \n", "            # 只处理目标变量\n", "            if var_name not in SURFACE_VARS and var_name not in UPPER_VARS:\n", "                continue\n", "                \n", "            # 获取经纬度\n", "            if lats is None:\n", "                lats, lons = grb.latlons()\n", "            \n", "            # 提取数据\n", "            data = grb.values.astype(np.float32)\n", "            \n", "            # 设置属性\n", "            long_name = SPECIAL_LONG_NAMES.get(var_name, getattr(grb, 'name', var_name))\n", "            attrs = {\n", "                'long_name': long_name,\n", "                'units': getattr(grb, 'units', 'unknown'),\n", "                'level_type': level_type,\n", "                'level': level if level_type != 'surface' else 0\n", "            }\n", "            \n", "            # 分类存储数据\n", "            forecast_time = ref_datetime + timedelta(hours=forecast_hour)\n", "            if var_name in SURFACE_VARS:\n", "                surface_data.append({\n", "                    'name': var_name,\n", "                    'data': data,\n", "                    'attrs': attrs,\n", "                    'time': forecast_time,\n", "                    'lats': lats,\n", "                    'lons': lons,\n", "                })\n", "                extracted_vars['surface'].add(var_name)\n", "            else:\n", "                level = level if level_type == 'isobaricInhPa' else 0\n", "                upper_data[var_name].append({\n", "                    'data': data,\n", "                    'attrs': attrs,\n", "                    'time': forecast_time,\n", "                    'level': level,\n", "                    'lats': lats,\n", "                    'lons': lons,\n", "                })\n", "                extracted_vars['upper'].add(var_name)\n", "        \n", "        grbs.close()\n", "        return surface_data, upper_data, forecast_time, extracted_vars\n", "    \n", "    except Exception as e:\n", "        print(f\"处理文件 {grib_file} 时出错: {str(e)}\")\n", "        return [], defaultdict(list), None, {'surface': set(), 'upper': set()}\n", "\n", "def save_surface_data(data_list, nc_file, times):\n", "    \"\"\"保存地面要素数据到NetCDF\"\"\"\n", "    var_dict = defaultdict(list)\n", "    for d in data_list:\n", "        var_dict[d['name']].append(d)\n", "\n", "    # 构建坐标\n", "    lats = data_list[0]['lats'][:, 0]\n", "    lons = data_list[0]['lons'][0, :]\n", "    \n", "    ds = xr.Dataset()\n", "    ds['latitude'] = xr.<PERSON>y(lats, dims=['latitude'], attrs={'units': 'degrees_north'})\n", "    ds['longitude'] = xr.<PERSON>y(lons, dims=['longitude'], attrs={'units': 'degrees_east'})\n", "    ds['time'] = xr.<PERSON>(times, dims=['time'], attrs={'standard_name': 'time'})\n", "\n", "    # 添加变量\n", "    for var_name, var_data in var_dict.items():\n", "        data_array = np.full((len(times), len(lats), len(lons)), np.nan, np.float32)\n", "        time_idx = {t: i for i, t in enumerate(times)}\n", "        \n", "        for d in var_data:\n", "            if d['time'] in time_idx:\n", "                data_array[time_idx[d['time']]] = d['data']\n", "        \n", "        attrs = {k: v for k, v in var_data[0]['attrs'].items() if v is not None}\n", "        ds[var_name] = xr.<PERSON>(data_array, dims=['time', 'latitude', 'longitude'], attrs=attrs)\n", "\n", "    # 全局属性\n", "    ds.attrs.update({\n", "        'Conventions': 'CF-1.6',\n", "        'title': 'Surface Meteorological Data',\n", "        'history': f'Created at {datetime.now()}',\n", "        'source': 'CMA_GFS',\n", "    })\n", "\n", "    # 压缩保存\n", "    encoding = {v: {'zlib': True, 'complevel': 1} for v in ds.variables}\n", "    ds.to_netcdf(nc_file, encoding=encoding, engine=\"netcdf4\")\n", "\n", "def save_upper_data(data_dict, levels, nc_file, times):\n", "    \"\"\"保存高空要素数据到NetCDF\"\"\"\n", "    ds = xr.Dataset()\n", "    \n", "    # 获取样本坐标\n", "    sample = next(iter(data_dict.values()))[0]\n", "    lats = sample['lats'][:, 0]\n", "    lons = sample['lons'][0, :]\n", "    \n", "    # 添加坐标\n", "    ds['latitude'] = xr.<PERSON>y(lats, dims=['latitude'], attrs={'units': 'degrees_north'})\n", "    ds['longitude'] = xr.<PERSON>y(lons, dims=['longitude'], attrs={'units': 'degrees_east'})\n", "    ds['pressure'] = xr.<PERSON>(levels, dims=['pressure'], \n", "                                attrs={'units': 'hPa', 'long_name': 'pressure level'})\n", "    ds['time'] = xr.<PERSON>(times, dims=['time'], attrs={'standard_name': 'time'})\n", "\n", "    # 添加变量\n", "    for var, data_list in data_dict.items():\n", "        data_array = np.full((len(times), len(levels), len(lats), len(lons)), np.nan, np.float32)\n", "        time_idx = {t: i for i, t in enumerate(times)}\n", "        level_idx = {l: i for i, l in enumerate(levels)}\n", "        \n", "        for d in data_list:\n", "            if d['time'] in time_idx and d['level'] in level_idx:\n", "                data_array[time_idx[d['time']], level_idx[d['level']]] = d['data']\n", "        \n", "        attrs = {k: v for k, v in data_list[0]['attrs'].items() if v is not None}\n", "        ds[var] = xr.<PERSON>(data_array, dims=['time', 'pressure', 'latitude', 'longitude'], attrs=attrs)\n", "\n", "    # 全局属性\n", "    ds.attrs.update({\n", "        'Conventions': 'CF-1.6',\n", "        'title': 'Upper Air Meteorological Data',\n", "        'history': f'Created at {datetime.now()}',\n", "        'source': 'CMA_GFS',\n", "    })\n", "\n", "    # 压缩保存\n", "    encoding = {v: {'zlib': True, 'complevel': 1} for v in ds.variables}\n", "    ds.to_netcdf(nc_file, encoding=encoding, engine=\"netcdf4\")\n", "\n", "def get_forecast_hours(hour_str):\n", "    \"\"\"根据起报时间的小时值返回预报时效\"\"\"\n", "    hour = int(hour_str)\n", "    if hour in [6, 18]:\n", "        return list(range(0, 123, 3))\n", "    elif hour in [0, 12]:\n", "        return list(range(0, 123, 3)) + list(range(126, 246, 6))\n", "    else:\n", "        return list(range(0, 123, 3))\n", "\n", "def generate_time_range(start_time, end_time, interval_hours=6):\n", "    \"\"\"生成时间范围内的所有起报时间\"\"\"\n", "    start = datetime.strptime(start_time, \"%Y%m%d%H%M%S\")\n", "    end = datetime.strptime(end_time, \"%Y%m%d%H%M%S\")\n", "    current = start\n", "    time_list = []\n", "    while current <= end:\n", "        time_list.append(current.strftime(\"%Y%m%d%H%M%S\"))\n", "        current += timedelta(hours=interval_hours)\n", "    return time_list\n", "\n", "def process_forecast_files(reference_time, forecast_hours, base_dir, output_dir, logger):\n", "    \"\"\"处理所有预报文件\"\"\"\n", "    total_start = time.time()\n", "    logger.log(reference_time, \"start\", 0, \"\")\n", "    \n", "    year = reference_time[:4]\n", "    ymdh = reference_time[:10]\n", "    grib_dir = os.path.join(base_dir, year, ymdh)\n", "    \n", "    if not os.path.exists(grib_dir):\n", "        logger.log(reference_time, \"error\", 0, f\"目录不存在: {grib_dir}\")\n", "        return False\n", "\n", "    # 1. 文件读取阶段\n", "    read_start = time.time()\n", "    args = []\n", "    planned_files = len(forecast_hours)\n", "    actual_files = 0\n", "    \n", "    for n in forecast_hours:\n", "        n_str = f\"{n:03d}\"\n", "        grib_file = os.path.join(grib_dir, f\"Z_NAFP_C_BABJ_{reference_time}_P_NWPC-GRAPES-GFS-HNEHE-{n_str}00.grib2\")\n", "        if os.path.exists(grib_file):\n", "            args.append((grib_file, datetime.strptime(reference_time, \"%Y%m%d%H%M%S\"), n))\n", "            actual_files += 1\n", "    \n", "    read_time = time.time() - read_start\n", "    logger.log(reference_time, \"读取文件\", read_time, f\"计划读取 {planned_files} 个文件, 实际读取 {actual_files} 个文件\")\n", "    \n", "    if not args:\n", "        logger.log(reference_time, \"error\", 0, \"没有找到有效的GRIB文件\")\n", "        return False\n", "\n", "    # 2. 变量提取阶段\n", "    extract_start = time.time()\n", "    with Pool() as pool:\n", "        results = pool.starmap(process_single_file, args)\n", "    \n", "    all_surface_data = []\n", "    all_upper_data = defaultdict(list)\n", "    valid_times = set()\n", "    pressure_levels = set()\n", "    extracted_vars = {'surface': set(), 'upper': set()}\n", "    \n", "    for sfc_data, upper_data, fc_time, vars_info in results:\n", "        if fc_time is not None:\n", "            valid_times.add(fc_time)\n", "            all_surface_data.extend(sfc_data)\n", "            extracted_vars['surface'].update(vars_info['surface'])\n", "            for var, data_list in upper_data.items():\n", "                all_upper_data[var].extend(data_list)\n", "                extracted_vars['upper'].update(vars_info['upper'])\n", "                for d in data_list:\n", "                    pressure_levels.add(d['level'])\n", "    \n", "    extract_time = time.time() - extract_start\n", "    surface_vars = \", \".join(f\"'{v}'\" for v in sorted(extracted_vars['surface']))\n", "    upper_vars = \", \".join(f\"'{v}'\" for v in sorted(extracted_vars['upper']))\n", "    logger.log(reference_time, \"提取变量\", extract_time, f\"高空变量: {upper_vars} | 地面变量: {surface_vars}\")\n", "\n", "    # 3. 数据保存阶段\n", "    save_start = time.time()\n", "    success = False\n", "    if all_surface_data or all_upper_data:\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        sorted_times = sorted(valid_times)\n", "        \n", "        save_msg = \"\"\n", "        if all_surface_data:\n", "            sfc_file = os.path.join(output_dir, f\"CMA_GFS_GXDSJ_{reference_time}_SURF.nc\")\n", "            try:\n", "                save_surface_data(all_surface_data, sfc_file, sorted_times)\n", "                save_msg += f\"SURF.NC -> {sfc_file} | \"\n", "                success = True\n", "            except Exception as e:\n", "                save_msg += f\"地面数据保存失败: {str(e)} | \"\n", "        \n", "        if all_upper_data:\n", "            upper_file = os.path.join(output_dir, f\"CMA_GFS_GXDSJ_{reference_time}_UPAR.nc\")\n", "            try:\n", "                save_upper_data(all_upper_data, sorted(pressure_levels, reverse=True), upper_file, sorted_times)\n", "                save_msg += f\"UPAR.NC -> {upper_file}\"\n", "                success = True\n", "            except Exception as e:\n", "                save_msg += f\"高空数据保存失败: {str(e)}\"\n", "        \n", "        save_time = time.time() - save_start\n", "        logger.log(reference_time, \"转存NC\", save_time, save_msg.strip(\" | \"))\n", "    \n", "    total_time = time.time() - total_start\n", "    logger.log(reference_time, \"end\", total_time, \"\")\n", "    return success\n", "\n", "if __name__ == \"__main__\":\n", "    # 配置参数\n", "    start_time = \"20220329120000\"\n", "    end_time = \"20220429120000\"\n", "    base_dir = \"/workspace/data/NAFP/CMA_GFS\"\n", "    output_dir = \"/workspace/output/CMA_GFS\"\n", "    \n", "    # 初始化日志\n", "    logger = ProcessingLogger(output_dir, start_time, end_time)\n", "    \n", "    # 生成所有起报时间\n", "    reference_times = generate_time_range(start_time, end_time)\n", "    \n", "    # 处理每个起报时间\n", "    processed_count = 0\n", "    failed_count = 0\n", "    \n", "    for ref_time in reference_times:\n", "        hour_str = ref_time[8:10]\n", "        forecast_hours = get_forecast_hours(hour_str)\n", "        \n", "        success = process_forecast_files(ref_time, forecast_hours, base_dir, output_dir, logger)\n", "        if success:\n", "            processed_count += 1\n", "        else:\n", "            failed_count += 1\n", "    \n", "    # 输出总结\n", "    summary = f\"\\n处理完成! 成功处理: {processed_count} 个, 失败: {failed_count} 个\"\n", "    print(summary)\n", "    with open(logger.log_file, 'a', encoding='utf-8') as f:\n", "        f.write(summary + \"\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "316e1022-e217-4d29-b714-89df2d590eab", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}