#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EC_2022_adapted.py的功能
验证对新GRIB2格式的支持
"""

import os
import sys
from EC_2022_adapted import ECMWFProcessorAdapted

def test_single_file():
    """测试单文件处理"""
    print("=" * 60)
    print("测试单文件处理")
    print("=" * 60)
    
    # 测试文件
    test_file = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        print("请将GRIB2文件放在当前目录下进行测试")
        return False
    
    # 创建处理器
    log_file = "test_adapted.log"
    processor = ECMWFProcessorAdapted(log_file)
    
    print(f"测试文件: {test_file}")
    
    try:
        # 测试文件名解析
        print("\n1. 测试文件名解析...")
        reference_time = processor._parse_new_format_time(test_file)
        print(f"   解析的参考时间: {reference_time}")
        
        # 测试数据读取
        print("\n2. 测试数据读取...")
        surface_ds, upper_ds = processor._process_single_grib_file_new_format(test_file)
        
        if surface_ds is not None:
            print(f"   ✓ 地面数据读取成功")
            print(f"     变量: {list(surface_ds.data_vars.keys())}")
            print(f"     维度: {dict(surface_ds.dims)}")
        else:
            print(f"   - 无地面数据")
        
        if upper_ds is not None:
            print(f"   ✓ 高空数据读取成功")
            print(f"     变量: {list(upper_ds.data_vars.keys())}")
            print(f"     维度: {dict(upper_ds.dims)}")
        else:
            print(f"   - 无高空数据")
        
        # 测试完整处理流程
        print("\n3. 测试完整处理流程...")
        output_dir = "test_output_adapted"
        success = processor.process_new_format_file(test_file, output_dir)
        
        if success:
            print("   ✓ 完整处理成功")
            
            # 检查输出文件
            if os.path.exists(output_dir):
                output_files = os.listdir(output_dir)
                print(f"   生成文件: {output_files}")
                
                # 验证NetCDF文件
                for nc_file in output_files:
                    if nc_file.endswith('.NC'):
                        nc_path = os.path.join(output_dir, nc_file)
                        file_size = os.path.getsize(nc_path) / 1024  # KB
                        print(f"   {nc_file}: {file_size:.1f} KB")
        else:
            print("   ✗ 完整处理失败")
            return False
        
        print("\n✓ 单文件测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 单文件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_processing():
    """测试批量处理"""
    print("\n" + "=" * 60)
    print("测试批量处理")
    print("=" * 60)
    
    # 查找当前目录下的GRIB2文件
    import glob
    grib_files = glob.glob("*.GRB2") + glob.glob("*.grb2")
    
    if not grib_files:
        print("当前目录下没有GRIB2文件，跳过批量处理测试")
        return True
    
    print(f"找到 {len(grib_files)} 个GRIB2文件: {grib_files}")
    
    # 创建处理器
    log_file = "test_batch_adapted.log"
    processor = ECMWFProcessorAdapted(log_file)
    
    try:
        # 批量处理
        output_dir = "test_batch_output_adapted"
        success = processor.batch_process_new_format_directory(".", output_dir)
        
        if success:
            print("✓ 批量处理测试成功")
            
            # 检查输出
            if os.path.exists(output_dir):
                output_files = os.listdir(output_dir)
                print(f"批量处理生成文件: {len(output_files)} 个")
                for f in output_files[:5]:  # 只显示前5个
                    print(f"  {f}")
                if len(output_files) > 5:
                    print(f"  ... 还有 {len(output_files)-5} 个文件")
        else:
            print("✗ 批量处理测试失败")
            return False
        
        print("✓ 批量处理测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 批量处理测试失败: {e}")
        return False

def test_variable_mapping():
    """测试变量映射"""
    print("\n" + "=" * 60)
    print("测试变量映射")
    print("=" * 60)
    
    processor = ECMWFProcessorAdapted()
    
    print("地面变量列表:")
    print(f"  原有变量: {len(processor.TYPE1_SURFACE_VARS)} 个")
    print(f"  新格式变量: {len(processor.NEW_FORMAT_SURFACE_VARS)} 个")
    print(f"  合并后总数: {len(processor.ALL_SURFACE_VARS)} 个")
    
    print("\n高空变量列表:")
    print(f"  高空变量: {processor.UPPER_VARS}")
    
    print("\n变量映射示例:")
    sample_mappings = list(processor.GRIB_TO_NC_VAR_MAP.items())[:10]
    for grib_name, nc_name in sample_mappings:
        print(f"  {grib_name} -> {nc_name}")
    
    print(f"\n总映射数: {len(processor.GRIB_TO_NC_VAR_MAP)} 个")
    
    # 检查新格式变量是否在映射中
    new_vars_in_map = [var for var in processor.NEW_FORMAT_SURFACE_VARS 
                       if var in processor.GRIB_TO_NC_VAR_MAP]
    print(f"新格式变量已映射: {new_vars_in_map}")
    
    print("✓ 变量映射测试通过！")
    return True

def main():
    """主测试函数"""
    print("EC_2022_adapted.py 功能测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import xarray
        import pygrib
        import numpy
        print("✓ 依赖库检查通过")
    except ImportError as e:
        print(f"✗ 依赖库缺失: {e}")
        print("请安装必要的依赖库")
        return
    
    # 运行测试
    tests = [
        ("变量映射测试", test_variable_mapping),
        ("单文件处理测试", test_single_file),
        ("批量处理测试", test_batch_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    # 测试结果
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！适配版本可以正常使用")
        print("\n使用方法:")
        print("1. 单文件处理: python EC_2022_adapted.py")
        print("2. 修改main()函数中的配置参数来适配你的需求")
        print("3. 支持的功能:")
        print("   - 单文件处理")
        print("   - 批量目录处理") 
        print("   - 时间序列处理")
        print("   - 保持原有的SURF.NC和UPAR.NC输出格式")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
