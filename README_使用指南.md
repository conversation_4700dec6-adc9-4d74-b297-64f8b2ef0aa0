# GRIB数据自动处理工具使用指南

## 概述

本工具包提供了完整的GRIB数据自动处理解决方案，包含以下主要组件：

### 🔧 核心处理器
1. `auto_grib_processor.py` - **自动GRIB处理器** (推荐使用)
2. `enhanced_batch_processor.py` - 增强批量处理器
3. `new_grib2_processor.py` - 新GRIB2格式处理器

### 📊 分析工具
4. `analyze_grib2_file.py` - GRIB2文件信息分析工具
5. `code_explanation_and_comparison.py` - 原代码解释和格式对比

### 🛠️ 辅助工具
6. `install_dependencies.py` - 依赖安装脚本
7. `test_auto_processor.py` - 自动处理器测试脚本

## 原始代码解释

### EC_2022_new.ipynb 的功能

原始的Jupyter notebook是一个完整的ECMWF数据处理系统，主要功能包括：

1. **数据解压**: 将.bz2压缩文件解压为GRIB文件
2. **文件重命名**: 标准化文件命名格式
3. **数据提取**: 从GRIB文件中提取气象变量
4. **格式转换**: 转换为NetCDF格式
5. **数据分类**: 根据预报时效分为6类文件类型

### 处理的数据类型

- **地面变量**: 温度、湿度、气压、风速等 (保存为 *_SURF.NC)
- **高空变量**: 不同气压层的气象要素 (保存为 *_UPAR.NC)

### 文件格式差异

| 特征 | 原格式 | 新格式 |
|------|--------|--------|
| 文件名 | W_NAFP_C_ECMF_*_P_C1D* | Z_NAFP_C_BABJ_*_HRCLDAS_*.GRB2 |
| 数据源 | ECMWF | HRCLDAS |
| 文件类型 | 压缩GRIB | 直接GRIB2 |
| 变量组织 | 多变量文件 | 单变量文件 |
| 命名规范 | 旧标准 | 新标准 |

## 使用步骤

### 步骤0: 安装依赖库

首先安装必要的依赖库：

```bash
python install_dependencies.py
```

这个脚本会自动检查和安装所需的库，包括：
- numpy, xarray, netcdf4 (基础库)
- pygrib (GRIB文件读取)
- cfgrib, eccodes (可选，用于更好的GRIB支持)

### 步骤1: 测试处理器

运行测试脚本验证环境：

```bash
python test_processor.py
```

### 步骤2: 分析新文件

运行分析脚本来了解新文件的结构：

```bash
python analyze_grib2_file.py
```

**注意**: 请将你的GRIB2文件重命名为脚本中指定的文件名，或修改脚本中的文件路径。

### 步骤3: 查看代码解释

运行对比分析脚本了解差异：

```bash
python code_explanation_and_comparison.py
```

### 步骤4: 自动处理GRIB数据 (推荐)

使用自动处理器，无需手动配置：

```bash
# 单文件自动处理
python auto_grib_processor.py

# 批量处理目录
python enhanced_batch_processor.py input_dir output_dir

# 按时间范围批量处理 (类似原有逻辑)
python enhanced_batch_processor.py input_dir output_dir --mode time_range --start_time 202401010000 --end_time 202401020000
```

## 🚀 新功能特点

### 自动检测和转换
- **自动识别变量**: 无需手动配置变量列表
- **智能分类**: 自动区分地面变量和高空变量
- **保留原有逻辑**: 维持EC_2022_new.ipynb的转换结构
- **多格式支持**: 同时支持新旧GRIB格式

### 增强的处理能力
- **双引擎支持**: cfgrib + pygrib备用方案
- **批量处理**: 支持目录、时间范围、递归处理
- **详细日志**: 完整的处理过程记录
- **错误恢复**: 单个文件失败不影响整体处理

## 文件名格式解析

### 新格式文件名结构

```
Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
│ │    │ │    │              │ │       │  │    │    │   │    │
│ │    │ │    │              │ │       │  │    │    │   │    └─ 数据时间
│ │    │ │    │              │ │       │  │    │    │   └─ 变量名
│ │    │ │    │              │ │       │  │    │    └─ 数据类型
│ │    │ │    │              │ │       │  │    └─ 分辨率
│ │    │ │    │              │ │       │  └─ 区域代码
│ │    │ │    │              │ │       └─ 数据类型
│ │    │ │    │              │ └─ 产品类型
│ │    │ │    │              └─ 产品标识
│ │    │ │    └─ 创建时间
│ │    │ └─ 中心代码
│ │    └─ 国家代码
│ └─ 数据源
└─ 标识符
```

### 各字段含义

- **Z**: WMO标准标识符
- **NAFP**: 数据源 (国家气象信息中心)
- **BABJ**: 中心代码 (北京)
- **20240101000535**: 文件创建时间 (年月日时分秒)
- **HRCLDAS**: 产品类型 (高分辨率陆面数据同化系统)
- **RT**: 实时数据
- **BENN**: 区域代码
- **0P01**: 分辨率 (0.01度)
- **HOR**: 水平数据
- **QAIR**: 变量名 (比湿)
- **2024010100**: 数据时间 (年月日时)

## 常见变量说明

| 变量代码 | 中文名称 | 英文名称 | 单位 |
|----------|----------|----------|------|
| QAIR | 比湿 | Specific Humidity | kg/kg |
| TEMP | 温度 | Temperature | K |
| PRES | 气压 | Pressure | Pa |
| UGRD | U风分量 | U-component of Wind | m/s |
| VGRD | V风分量 | V-component of Wind | m/s |
| RH | 相对湿度 | Relative Humidity | % |
| PRCP | 降水 | Precipitation | mm |

## 输出文件格式

### 自动处理器输出 (保留原有结构)

处理后会生成两个NetCDF文件，与原有EC_2022_new.ipynb逻辑一致：

1. **`*_SURF.NC`** - 地面数据
   - 包含地面气象要素 (温度、湿度、气压、风速等)
   - 维度: [time, latitude, longitude, level]

2. **`*_UPAR.NC`** - 高空数据
   - 包含不同气压层的气象要素
   - 维度: [time, isobaricInhPa, latitude, longitude]

### 文件内容特点
- **时间维度**: 数据的有效时间
- **空间维度**: 经纬度网格
- **变量数据**: 保持原始GRIB变量名或标准化名称
- **完整属性**: 单位、长名称、层次信息等元数据
- **压缩存储**: 使用zlib压缩减少文件大小

## 注意事项

1. **依赖库**: 确保安装了 `xarray`, `pygrib`, `numpy` 等必要库
2. **文件路径**: 根据实际情况修改脚本中的文件路径
3. **内存使用**: 大文件处理时注意内存占用
4. **错误处理**: 如遇到读取错误，检查GRIB2文件的完整性

## 扩展功能

如需处理更多变量或添加新功能，可以：

1. 在 `VARIABLE_MAPPING` 中添加新的变量映射
2. 修改 `_preprocess_dataset` 函数添加数据预处理逻辑
3. 调整输出文件的命名规则和属性设置

## 故障排除

### 常见问题及解决方案

1. **cfgrib引擎不可用**
   ```
   错误: unrecognized engine cfgrib must be one of: ['netcdf4', 'scipy', 'store']
   解决: 运行 python install_dependencies.py 安装cfgrib
   备用: 处理器会自动使用pygrib作为备用方案
   ```

2. **变量名不匹配**
   ```
   问题: 文件名显示QAIR，但实际是2r (相对湿度)
   解决: 处理器已更新变量映射，会自动处理这种情况
   ```

3. **文件名解析失败**
   ```
   检查文件名格式是否符合: Z_NAFP_C_BABJ_*_HRCLDAS_*.GRB2
   ```

4. **GRIB读取错误**
   ```
   确认文件是有效的GRIB2格式
   尝试用其他GRIB工具验证文件完整性
   ```

5. **依赖库安装问题**
   ```
   Windows用户: 使用Anaconda/Miniconda更容易安装
   Linux用户: 可能需要安装系统级的GRIB库
   ```

### 调试建议

1. 运行 `python test_processor.py` 进行全面测试
2. 检查 `python install_dependencies.py` 的输出
3. 先用小文件测试处理流程
4. 检查中间输出的数据结构
5. 验证坐标系统和投影信息

## 联系支持

如有问题或需要进一步的定制化开发，请提供：

1. 具体的错误信息
2. 示例数据文件
3. 期望的输出格式
4. 特殊的处理需求
