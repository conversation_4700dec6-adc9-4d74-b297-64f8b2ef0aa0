#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多变量处理
验证GRIB2文件中的所有变量都能正确转换到NetCDF
"""

import os
import xarray as xr
import pygrib
from EC_2022_adapted import ECMWFProcessorAdapted

def analyze_grib_variables(file_path):
    """分析GRIB文件中的所有变量"""
    print(f"分析GRIB文件: {os.path.basename(file_path)}")
    print("=" * 50)
    
    try:
        with pygrib.open(file_path) as grbs:
            messages = list(grbs)
            print(f"GRIB消息总数: {len(messages)}")
            
            variables = {}
            for i, grb in enumerate(messages):
                var_name = grb.shortName
                var_long_name = grb.name
                units = grb.units
                level_type = grb.typeOfLevel
                level_value = grb.level
                
                if var_name not in variables:
                    variables[var_name] = {
                        'long_name': var_long_name,
                        'units': units,
                        'level_types': set(),
                        'levels': set(),
                        'count': 0
                    }
                
                variables[var_name]['level_types'].add(level_type)
                variables[var_name]['levels'].add(level_value)
                variables[var_name]['count'] += 1
            
            print(f"\n发现的变量 ({len(variables)} 个):")
            for var_name, info in variables.items():
                print(f"  {var_name}:")
                print(f"    长名称: {info['long_name']}")
                print(f"    单位: {info['units']}")
                print(f"    消息数: {info['count']}")
                print(f"    层次类型: {', '.join(info['level_types'])}")
                print(f"    层次值: {', '.join(map(str, sorted(info['levels'])))}")
                print()
            
            return variables
            
    except Exception as e:
        print(f"分析失败: {e}")
        return {}

def test_cfgrib_reading(file_path):
    """测试cfgrib读取"""
    print("测试cfgrib读取:")
    print("-" * 30)
    
    try:
        # 尝试读取所有数据
        ds = xr.open_dataset(file_path, engine='cfgrib')
        print(f"✓ cfgrib读取成功")
        print(f"  数据变量: {list(ds.data_vars.keys())}")
        print(f"  坐标: {list(ds.coords.keys())}")
        print(f"  维度: {dict(ds.dims)}")
        
        # 显示每个变量的详细信息
        for var_name in ds.data_vars:
            var = ds[var_name]
            print(f"  {var_name}: {var.dims} {var.shape}")
            if hasattr(var, 'attrs'):
                if 'long_name' in var.attrs:
                    print(f"    长名称: {var.attrs['long_name']}")
                if 'units' in var.attrs:
                    print(f"    单位: {var.attrs['units']}")
        
        return ds
        
    except Exception as e:
        print(f"✗ cfgrib读取失败: {e}")
        return None

def test_processor_conversion(file_path):
    """测试处理器转换"""
    print("\n测试处理器转换:")
    print("-" * 30)
    
    processor = ECMWFProcessorAdapted()
    
    try:
        # 测试数据读取
        surface_ds, upper_ds = processor._process_single_grib_file_new_format(file_path)
        
        print("处理器读取结果:")
        if surface_ds is not None:
            print(f"✓ 地面数据读取成功")
            print(f"  变量: {list(surface_ds.data_vars.keys())}")
            print(f"  维度: {dict(surface_ds.dims)}")
        else:
            print("✗ 未读取到地面数据")
        
        if upper_ds is not None:
            print(f"✓ 高空数据读取成功")
            print(f"  变量: {list(upper_ds.data_vars.keys())}")
            print(f"  维度: {dict(upper_ds.dims)}")
        else:
            print("- 未读取到高空数据")
        
        return surface_ds, upper_ds
        
    except Exception as e:
        print(f"✗ 处理器转换失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_full_conversion(file_path):
    """测试完整转换流程"""
    print("\n测试完整转换流程:")
    print("-" * 30)
    
    processor = ECMWFProcessorAdapted("./test_multi_var.log")
    output_dir = "test_multi_var_output"
    
    try:
        success = processor.process_new_format_file(file_path, output_dir)
        
        if success:
            print("✓ 完整转换成功")
            
            # 检查输出文件
            if os.path.exists(output_dir):
                output_files = os.listdir(output_dir)
                print(f"生成文件: {output_files}")
                
                # 分析输出的NetCDF文件
                for nc_file in output_files:
                    if nc_file.endswith('.NC'):
                        nc_path = os.path.join(output_dir, nc_file)
                        print(f"\n分析输出文件: {nc_file}")
                        
                        with xr.open_dataset(nc_path) as ds:
                            print(f"  变量: {list(ds.data_vars.keys())}")
                            print(f"  维度: {dict(ds.dims)}")
                            print(f"  坐标: {list(ds.coords.keys())}")
                            
                            # 检查每个变量
                            for var_name in ds.data_vars:
                                var = ds[var_name]
                                print(f"  {var_name}:")
                                print(f"    形状: {var.shape}")
                                print(f"    维度: {var.dims}")
                                if 'long_name' in var.attrs:
                                    print(f"    长名称: {var.attrs['long_name']}")
                                if 'units' in var.attrs:
                                    print(f"    单位: {var.attrs['units']}")
            
            return True
        else:
            print("✗ 完整转换失败")
            return False
            
    except Exception as e:
        print(f"✗ 完整转换异常: {e}")
        return False

def main():
    """主测试函数"""
    print("多变量处理测试程序")
    print("=" * 60)
    
    # 测试文件
    test_file = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        print("请将GRIB2文件放在当前目录下进行测试")
        return
    
    # 1. 分析GRIB文件变量
    grib_variables = analyze_grib_variables(test_file)
    
    # 2. 测试cfgrib读取
    cfgrib_ds = test_cfgrib_reading(test_file)
    
    # 3. 测试处理器转换
    surface_ds, upper_ds = test_processor_conversion(test_file)
    
    # 4. 测试完整转换
    conversion_success = test_full_conversion(test_file)
    
    # 5. 结果对比
    print("\n" + "=" * 60)
    print("结果对比:")
    print("=" * 60)
    
    if grib_variables:
        print(f"GRIB文件中的变量数: {len(grib_variables)}")
        print(f"变量列表: {list(grib_variables.keys())}")
    
    if cfgrib_ds:
        print(f"cfgrib读取的变量数: {len(cfgrib_ds.data_vars)}")
        print(f"变量列表: {list(cfgrib_ds.data_vars.keys())}")
    
    if surface_ds:
        print(f"处理器地面变量数: {len(surface_ds.data_vars)}")
        print(f"变量列表: {list(surface_ds.data_vars.keys())}")
    
    if conversion_success:
        print("✓ 完整转换流程成功")
    else:
        print("✗ 完整转换流程失败")
    
    # 检查是否所有变量都被保留
    if grib_variables and surface_ds:
        grib_var_count = len(grib_variables)
        surface_var_count = len(surface_ds.data_vars)
        
        if surface_var_count >= grib_var_count:
            print("🎉 所有变量都被成功转换！")
        else:
            print(f"⚠️  变量数量不匹配: GRIB({grib_var_count}) vs NetCDF({surface_var_count})")
            missing_vars = set(grib_variables.keys()) - set(surface_ds.data_vars.keys())
            if missing_vars:
                print(f"缺失的变量: {missing_vars}")

if __name__ == "__main__":
    main()
