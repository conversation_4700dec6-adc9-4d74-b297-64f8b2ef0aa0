#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EC_2022_new.ipynb 代码解释和新数据格式对比分析
"""

import os
import re
from datetime import datetime, timedelta

def explain_original_code():
    """解释原始代码的功能和数据处理流程"""
    
    print("="*80)
    print("EC_2022_new.ipynb 代码功能解释")
    print("="*80)
    
    print("""
1. 代码总体功能:
   - 这是一个ECMWF数据处理程序，用于批量处理欧洲中期天气预报中心的数据
   - 主要功能是将压缩的GRIB格式文件转换为NetCDF格式，便于后续分析使用

2. 数据处理流程:
   a) 解压缩: 将.bz2压缩文件解压为GRIB文件
   b) 文件重命名: 将原始文件名标准化为统一格式
   c) 数据提取: 从GRIB文件中提取气象变量数据
   d) 格式转换: 将数据转换为NetCDF格式并保存
   e) 清理: 删除临时文件

3. 原始数据文件格式:
   - 输入: 压缩的.bz2文件，包含GRIB格式的气象数据
   - 文件名格式: W_NAFP_C_ECMF_*_P_C1D*
   - 数据来源: ECMWF (欧洲中期天气预报中心)

4. 处理的变量类型:
   a) 地面变量: 温度、湿度、气压、风速等地面气象要素
   b) 高空变量: 不同气压层的温度、湿度、风场等高空气象要素

5. 文件分类系统:
   - 根据预报时效将文件分为6类 (TYPE1-TYPE6)
   - 每类包含不同的气象变量组合
   - 不同类型的文件有不同的处理逻辑

6. 输出结果:
   - 生成两个NetCDF文件: *_SURF.NC (地面数据) 和 *_UPAR.NC (高空数据)
   - 包含完整的时间序列和空间网格数据
""")

def analyze_filename_differences():
    """分析新旧文件名格式的差异"""
    
    print("\n" + "="*80)
    print("新旧文件名格式对比分析")
    print("="*80)
    
    print("""
原始文件名格式 (EC_2022_new.ipynb处理的格式):
  格式: W_NAFP_C_ECMF_*_P_C1D*
  示例: W_NAFP_C_ECMF_20220101_P_C1D0101120001011201
  
  解析规则:
  - W_NAFP_C_ECMF: 固定前缀，表示数据来源
  - 时间信息: 包含参考时间和预报时间
  - 文件类型: C1D表示特定的数据类型

新文件名格式 (你提供的格式):
  格式: Z_NAFP_C_BABJ_YYYYMMDDHHMMSS_P_HRCLDAS_RT_BENN_0P01_HOR-VARIABLE-YYYYMMDDHH.GRB2
  示例: Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
  
  解析规则:
  - Z_NAFP_C_BABJ: 新的数据源标识
  - 20240101000535: 文件创建时间 (年月日时分秒)
  - HRCLDAS: 产品类型 (高分辨率陆面数据同化系统)
  - RT: 实时数据标识
  - BENN: 区域代码
  - 0P01: 分辨率 (0.01度)
  - HOR: 水平数据
  - QAIR: 变量名 (比湿)
  - 2024010100: 数据时间 (年月日时)
  - GRB2: GRIB2格式

主要差异:
1. 数据源: ECMWF → HRCLDAS (不同的数据产品)
2. 文件格式: 直接GRIB → GRIB2
3. 命名规范: 完全不同的命名体系
4. 变量组织: 单变量文件 vs 多变量文件
5. 时间表示: 不同的时间编码方式
""")

def generate_adaptation_suggestions():
    """生成代码适配建议"""
    
    print("\n" + "="*80)
    print("代码适配建议")
    print("="*80)
    
    print("""
要处理新格式的GRIB2文件，需要进行以下修改:

1. 文件名解析函数修改:
   - 更新 _parse_time_from_filename() 函数
   - 添加新的正则表达式模式匹配
   - 适配新的时间格式解析

2. 文件分类逻辑调整:
   - 新格式是单变量文件，不需要复杂的变量分类
   - 可能需要按变量类型重新组织处理逻辑

3. 变量映射更新:
   - 检查 GRIB_TO_NC_VAR_MAP 映射表
   - 添加新变量的映射关系 (如 QAIR)

4. 数据读取适配:
   - 验证 xarray 和 cfgrib 对 GRIB2 的支持
   - 可能需要调整读取参数

5. 输出格式调整:
   - 根据新数据的特点调整输出文件命名
   - 可能需要不同的数据组织方式

建议的处理步骤:
1. 先运行 analyze_grib2_file.py 分析新文件的具体内容
2. 根据分析结果确定需要修改的具体部分
3. 创建新的处理类或修改现有的 ECMWFProcessor 类
4. 测试新格式文件的处理效果
""")

def create_new_filename_parser():
    """创建新文件名解析器示例"""
    
    print("\n" + "="*80)
    print("新文件名解析器示例代码")
    print("="*80)
    
    code_example = '''
def parse_new_filename(filename):
    """解析新格式的GRIB2文件名"""
    # Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
    pattern = r'Z_(\\w+)_C_(\\w+)_(\\d{14})_P_(\\w+)_(\\w+)_(\\w+)_(\\w+)_(\\w+)-(\\w+)-(\\d{10})\\.(\\w+)'
    
    match = re.match(pattern, filename)
    if not match:
        raise ValueError(f"无法解析文件名: {filename}")
    
    groups = match.groups()
    
    # 解析时间信息
    creation_time = datetime.strptime(groups[2], "%Y%m%d%H%M%S")
    data_time = datetime.strptime(groups[9], "%Y%m%d%H")
    
    return {
        'data_source': groups[0],      # NAFP
        'center_code': groups[1],      # BABJ
        'creation_time': creation_time,
        'product_type': groups[3],     # HRCLDAS
        'data_type': groups[4],        # RT
        'region': groups[5],           # BENN
        'resolution': groups[6],       # 0P01
        'variable_info': groups[7],    # HOR
        'variable_name': groups[8],    # QAIR
        'data_time': data_time,
        'file_format': groups[10]      # GRB2
    }

# 使用示例
filename = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
info = parse_new_filename(filename)
print(f"变量名: {info['variable_name']}")
print(f"数据时间: {info['data_time']}")
print(f"分辨率: {info['resolution']}")
'''
    
    print(code_example)

def main():
    """主函数"""
    explain_original_code()
    analyze_filename_differences()
    generate_adaptation_suggestions()
    create_new_filename_parser()
    
    print("\n" + "="*80)
    print("总结")
    print("="*80)
    print("""
原始代码是一个完整的ECMWF数据处理系统，功能强大但针对特定的数据格式。
新的GRIB2文件格式完全不同，需要重新设计处理逻辑。

建议的处理方案:
1. 保留原有代码的核心处理框架 (解压、转换、保存)
2. 重写文件名解析和数据读取部分
3. 根据新数据的特点调整变量处理逻辑
4. 创建适配新格式的处理类

下一步: 运行 analyze_grib2_file.py 来详细分析你的GRIB2文件内容。
""")

if __name__ == "__main__":
    main()
