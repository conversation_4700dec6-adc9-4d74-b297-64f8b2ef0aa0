#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的批量GRIB处理器
结合原有EC_2022_new.ipynb的逻辑和新的自动检测功能
"""

import os
import sys
import glob
import shutil
from datetime import datetime, timedelta
from pathlib import Path
import argparse
from auto_grib_processor import AutoGRIBProcessor

class EnhancedBatchProcessor:
    """增强的批量处理器"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        # 创建主日志文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.main_log = os.path.join(log_dir, f"batch_processing_{timestamp}.log")
        
        # 初始化处理器
        self.processor = AutoGRIBProcessor(self.main_log)
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'start_time': datetime.now()
        }
    
    def log_message(self, message, level="INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
        log_entry = f"{timestamp} [{level}] {message}"
        
        print(log_entry)
        with open(self.main_log, 'a', encoding='utf-8') as f:
            f.write(log_entry + "\n")
    
    def process_single_directory(self, input_dir, output_dir, file_patterns=None):
        """处理单个目录"""
        if file_patterns is None:
            file_patterns = ["*.GRB2", "*.GRB", "*.grb2", "*.grb"]
        
        self.log_message(f"开始处理目录: {input_dir}")
        self.log_message(f"输出目录: {output_dir}")
        self.log_message(f"文件模式: {file_patterns}")
        
        # 查找所有匹配的文件
        all_files = []
        for pattern in file_patterns:
            search_path = os.path.join(input_dir, pattern)
            found_files = glob.glob(search_path)
            all_files.extend(found_files)
        
        # 去重并排序
        all_files = sorted(list(set(all_files)))
        
        if not all_files:
            self.log_message(f"未找到匹配的GRIB文件", "WARNING")
            return False
        
        self.log_message(f"找到 {len(all_files)} 个GRIB文件")
        self.stats['total_files'] += len(all_files)
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 处理每个文件
        for i, file_path in enumerate(all_files, 1):
            filename = os.path.basename(file_path)
            self.log_message(f"处理文件 {i}/{len(all_files)}: {filename}")
            
            try:
                # 检查文件大小
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                if file_size > 1000:  # 大于1GB的文件给出警告
                    self.log_message(f"大文件警告: {filename} ({file_size:.1f} MB)", "WARNING")
                
                # 自动检测并处理
                if self.processor.auto_detect_grib_structure(file_path):
                    if self.processor.auto_convert_grib_to_nc(file_path, output_dir):
                        self.stats['processed_files'] += 1
                        self.log_message(f"✓ 成功处理: {filename}")
                    else:
                        self.stats['failed_files'] += 1
                        self.log_message(f"✗ 转换失败: {filename}", "ERROR")
                else:
                    self.stats['failed_files'] += 1
                    self.log_message(f"✗ 结构检测失败: {filename}", "ERROR")
                
                # 重置检测状态
                self.processor._reset_detection()
                
            except Exception as e:
                self.stats['failed_files'] += 1
                self.log_message(f"✗ 处理异常: {filename} - {str(e)}", "ERROR")
                continue
        
        return True
    
    def process_time_range_directory(self, base_dir, output_dir, start_time, end_time, time_step_hours=12):
        """按时间范围处理目录结构 (类似原有的时间范围处理)"""
        self.log_message(f"按时间范围处理: {start_time} 到 {end_time}")
        
        current_time = datetime.strptime(start_time, "%Y%m%d%H%M")
        end_dt = datetime.strptime(end_time, "%Y%m%d%H%M")
        
        while current_time <= end_dt:
            time_str = current_time.strftime("%Y%m%d%H%M")
            year_str = current_time.strftime("%Y")
            date_str = current_time.strftime("%Y%m%d%H")
            
            # 构建可能的目录路径
            possible_dirs = [
                os.path.join(base_dir, year_str, date_str),
                os.path.join(base_dir, date_str),
                os.path.join(base_dir, year_str, time_str),
                os.path.join(base_dir, time_str)
            ]
            
            # 查找存在的目录
            found_dir = None
            for dir_path in possible_dirs:
                if os.path.exists(dir_path):
                    found_dir = dir_path
                    break
            
            if found_dir:
                self.log_message(f"处理时间点: {time_str}")
                time_output_dir = os.path.join(output_dir, date_str)
                self.process_single_directory(found_dir, time_output_dir)
            else:
                self.log_message(f"跳过时间点: {time_str} (目录不存在)", "WARNING")
                self.stats['skipped_files'] += 1
            
            current_time += timedelta(hours=time_step_hours)
    
    def process_recursive(self, root_dir, output_dir, max_depth=3):
        """递归处理目录树"""
        self.log_message(f"递归处理目录树: {root_dir} (最大深度: {max_depth})")
        
        for root, dirs, files in os.walk(root_dir):
            # 计算当前深度
            depth = root.replace(root_dir, '').count(os.sep)
            if depth >= max_depth:
                dirs[:] = []  # 不再深入
                continue
            
            # 检查是否有GRIB文件
            grib_files = [f for f in files if f.lower().endswith(('.grb', '.grb2', '.grib', '.grib2'))]
            
            if grib_files:
                # 创建相对输出路径
                rel_path = os.path.relpath(root, root_dir)
                current_output_dir = os.path.join(output_dir, rel_path)
                
                self.log_message(f"处理子目录: {rel_path} ({len(grib_files)} 个文件)")
                self.process_single_directory(root, current_output_dir)
    
    def generate_summary_report(self, output_dir):
        """生成处理摘要报告"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        report_path = os.path.join(output_dir, "processing_summary.txt")
        
        report_content = f"""
GRIB批量处理摘要报告
{'='*50}

处理时间: {self.stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}
总耗时: {duration}

文件统计:
- 总文件数: {self.stats['total_files']}
- 成功处理: {self.stats['processed_files']}
- 处理失败: {self.stats['failed_files']}
- 跳过文件: {self.stats['skipped_files']}

成功率: {(self.stats['processed_files']/max(1, self.stats['total_files'])*100):.1f}%

输出目录: {output_dir}
日志文件: {self.main_log}

{'='*50}
"""
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        self.log_message("处理摘要报告已生成")
        print(report_content)
        
        return report_path

def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description="增强的GRIB批量处理器")
    parser.add_argument("input_dir", help="输入目录路径")
    parser.add_argument("output_dir", help="输出目录路径")
    parser.add_argument("--mode", choices=["single", "time_range", "recursive"], 
                       default="single", help="处理模式")
    parser.add_argument("--start_time", help="开始时间 (YYYYMMDDHHMM)")
    parser.add_argument("--end_time", help="结束时间 (YYYYMMDDHHMM)")
    parser.add_argument("--time_step", type=int, default=12, help="时间步长(小时)")
    parser.add_argument("--max_depth", type=int, default=3, help="递归最大深度")
    parser.add_argument("--patterns", nargs="+", default=["*.GRB2", "*.GRB"], 
                       help="文件匹配模式")
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = EnhancedBatchProcessor()
    
    try:
        if args.mode == "single":
            # 单目录处理
            processor.process_single_directory(args.input_dir, args.output_dir, args.patterns)
            
        elif args.mode == "time_range":
            # 时间范围处理
            if not args.start_time or not args.end_time:
                print("时间范围模式需要指定 --start_time 和 --end_time")
                sys.exit(1)
            processor.process_time_range_directory(
                args.input_dir, args.output_dir, 
                args.start_time, args.end_time, args.time_step
            )
            
        elif args.mode == "recursive":
            # 递归处理
            processor.process_recursive(args.input_dir, args.output_dir, args.max_depth)
        
        # 生成摘要报告
        processor.generate_summary_report(args.output_dir)
        
    except KeyboardInterrupt:
        processor.log_message("用户中断处理", "WARNING")
    except Exception as e:
        processor.log_message(f"处理过程中出现错误: {e}", "ERROR")
        raise

if __name__ == "__main__":
    # 如果没有命令行参数，运行示例
    if len(sys.argv) == 1:
        print("增强批量处理器示例运行...")
        processor = EnhancedBatchProcessor()
        
        # 示例：处理当前目录的GRIB文件
        current_dir = "."
        output_dir = "batch_output"
        
        processor.process_single_directory(current_dir, output_dir)
        processor.generate_summary_report(output_dir)
    else:
        main()
