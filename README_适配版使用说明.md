# EC_2022_adapted.py 使用说明

## 概述

`EC_2022_adapted.py` 是基于原有 `EC_2022_new.ipynb` 代码的**最小化修改版本**，专门用于处理新的GRIB2格式文件：

```
Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
```

## 🎯 设计原则

- ✅ **保持原有逻辑**: 完全保留原代码的处理流程和数据结构
- ✅ **最小化修改**: 只修改必要的文件名解析和变量映射部分
- ✅ **输出格式一致**: 仍然生成 `*_SURF.NC` 和 `*_UPAR.NC` 文件
- ✅ **向后兼容**: 支持原有的变量分类和命名规则

## 📁 主要修改内容

### 1. 文件名解析
- 新增 `_parse_new_format_time()` 方法解析新格式文件名
- 支持从文件名提取时间信息: `2024010100` → `202401010000`

### 2. 变量扩展
- 保留原有的所有变量分类 (`TYPE1_SURFACE_VARS` 等)
- 新增 `NEW_FORMAT_SURFACE_VARS` 支持新格式常见变量
- 扩展 `GRIB_TO_NC_VAR_MAP` 映射表

### 3. 数据读取增强
- 新增 `_process_single_grib_file_new_format()` 方法
- 支持 cfgrib + pygrib 双引擎读取
- 保持原有的数据过滤和重命名逻辑

## 🚀 使用方法

### 快速开始

```bash
# 1. 将你的GRIB2文件放在当前目录
# 2. 直接运行
python EC_2022_adapted.py
```

### 测试功能

```bash
# 运行测试脚本
python test_adapted.py
```

### 自定义配置

修改 `EC_2022_adapted.py` 中的 `main()` 函数：

```python
# 配置你的文件路径
SINGLE_FILE = "你的GRIB2文件.GRB2"
OUTPUT_DIR = "输出目录"
BATCH_INPUT_DIR = "批量输入目录"
```

## 📊 支持的处理模式

### 1. 单文件处理
```python
processor = ECMWFProcessorAdapted("log.txt")
success = processor.process_new_format_file("input.GRB2", "output_dir")
```

### 2. 批量目录处理
```python
success = processor.batch_process_new_format_directory(
    "input_dir", 
    "output_dir", 
    pattern="*.GRB2"
)
```

### 3. 时间序列处理
```python
success = processor.process_time_series_new_format(
    "input_dir",
    "output_dir", 
    "202401010000",  # 开始时间
    "202401010300",  # 结束时间
    time_step_hours=1
)
```

## 📋 输出文件格式

### 保持原有格式
- **地面数据**: `HRCLDAS_GRIB_202401010000_SURF.NC`
- **高空数据**: `HRCLDAS_GRIB_202401010000_UPAR.NC`

### 数据结构
```
地面数据 (SURF.NC):
- 维度: [time, latitude, longitude, level]
- 变量: rh2m, t2m, sp, u10, v10 等

高空数据 (UPAR.NC):
- 维度: [time, isobaricInhPa, latitude, longitude]  
- 变量: u, v, t, q, gh 等
```

## 🔧 变量映射

### 新格式变量支持
| GRIB变量 | NetCDF变量 | 描述 |
|----------|------------|------|
| 2r | rh2m | 2米相对湿度 |
| 2t | t2m | 2米温度 |
| 2d | d2m | 2米露点温度 |
| sp | sp | 地面气压 |
| 10u | u10 | 10米U风 |
| 10v | v10 | 10米V风 |

### 保留原有映射
所有原有的变量映射关系保持不变，确保向后兼容。

## ⚙️ 配置选项

### 变量过滤
```python
# 地面变量 (自动合并原有和新格式)
self.ALL_SURFACE_VARS = list(set(
    self.TYPE1_SURFACE_VARS + 
    self.NEW_FORMAT_SURFACE_VARS
))

# 高空变量 (保持原有)
self.UPPER_VARS = ['v', 'u', 'd', 't', 'q', 'gh', 'pv', 'r', 'w']
```

### 编码设置 (保持原有)
```python
encoding = {
    'zlib': True,
    'complevel': 1,
    'dtype': 'float32',
    '_FillValue': None
}
```

## 🐛 故障排除

### 常见问题

1. **文件名解析失败**
   ```
   问题: 无法解析文件名格式
   解决: 确认文件名符合 Z_NAFP_C_BABJ_*_HRCLDAS_*.GRB2 格式
   ```

2. **变量读取失败**
   ```
   问题: 未找到预期的变量
   解决: 检查 ALL_SURFACE_VARS 和 UPPER_VARS 列表
   ```

3. **cfgrib读取错误**
   ```
   问题: cfgrib引擎失败
   解决: 自动切换到pygrib备用方案
   ```

### 调试建议

1. 运行 `python test_adapted.py` 进行全面测试
2. 检查日志文件中的详细错误信息
3. 确认GRIB2文件的完整性和格式

## 📈 性能特点

- **内存优化**: 保持原有的chunk和编码策略
- **并发处理**: 支持批量文件的顺序处理
- **错误恢复**: 单个文件失败不影响批量处理
- **日志记录**: 详细的处理过程和时间统计

## 🔄 与原版本的区别

| 特性 | 原版本 | 适配版本 |
|------|--------|----------|
| 支持格式 | W_NAFP_C_ECMF_* | Z_NAFP_C_BABJ_*_HRCLDAS_* |
| 文件组织 | 多变量压缩文件 | 单变量GRIB2文件 |
| 处理逻辑 | 复杂的文件分类 | 简化的直接处理 |
| 输出格式 | SURF.NC + UPAR.NC | 保持相同 |
| 变量映射 | 原有映射 | 扩展映射 |

## 📞 技术支持

如遇到问题，请提供：
1. 具体的GRIB2文件名
2. 错误日志信息
3. 期望的处理结果
4. 系统环境信息

---

**总结**: 这个适配版本保持了原有代码的所有优点，只是增加了对新GRIB2格式的支持，使用方式基本不变。
