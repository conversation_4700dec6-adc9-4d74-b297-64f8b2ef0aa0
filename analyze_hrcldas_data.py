#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HRCLDAS数据格式分析工具
用于分析Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2格式数据
"""

import os
import sys
import re
import pygrib
import xarray as xr
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import traceback

class HRCLDASAnalyzer:
    """HRCLDAS数据分析器"""
    
    def __init__(self):
        self.file_patterns = {
            'hrcldas': r'Z_NAFP_C_BABJ_(\d{14})_P_HRCLDAS_RT_BENN_0P01_HOR-(\w+)-(\d{10})\.GRB2?'
        }
    
    def parse_filename(self, filename):
        """解析HRCLDAS文件名"""
        filename = Path(filename).name
        
        # HRCLDAS文件名格式: Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
        pattern = self.file_patterns['hrcldas']
        match = re.match(pattern, filename)
        
        if match:
            creation_time = match.group(1)  # 文件创建时间 20240101000535
            variable = match.group(2)       # 变量名 QAIR
            data_time = match.group(3)      # 数据时间 2024010100
            
            # 解析时间
            try:
                creation_dt = datetime.strptime(creation_time, "%Y%m%d%H%M%S")
                data_dt = datetime.strptime(data_time, "%Y%m%d%H")
                
                return {
                    'file_type': 'HRCLDAS',
                    'creation_time': creation_dt,
                    'data_time': data_dt,
                    'variable': variable,
                    'resolution': '0.01°',
                    'domain': 'BENN',
                    'format': 'horizontal',
                    'original_filename': filename
                }
            except ValueError as e:
                print(f"时间解析错误: {e}")
                return None
        
        return None
    
    def analyze_grib_file(self, filepath):
        """分析GRIB文件内容"""
        if not os.path.exists(filepath):
            print(f"文件不存在: {filepath}")
            return None
            
        print(f"\n{'='*60}")
        print(f"分析文件: {Path(filepath).name}")
        print(f"{'='*60}")
        
        # 解析文件名
        file_info = self.parse_filename(filepath)
        if file_info:
            print(f"文件类型: {file_info['file_type']}")
            print(f"创建时间: {file_info['creation_time']}")
            print(f"数据时间: {file_info['data_time']}")
            print(f"变量名称: {file_info['variable']}")
            print(f"空间分辨率: {file_info['resolution']}")
            print(f"覆盖区域: {file_info['domain']}")
            print(f"数据格式: {file_info['format']}")
        
        try:
            # 使用pygrib分析
            print(f"\n--- PYGRIB分析结果 ---")
            with pygrib.open(filepath) as grbs:
                message_count = 0
                variables = set()
                levels = set()
                times = set()
                
                for grb in grbs:
                    message_count += 1
                    
                    # 基本信息
                    short_name = getattr(grb, 'shortName', 'unknown')
                    name = getattr(grb, 'name', 'unknown')
                    units = getattr(grb, 'units', 'unknown')
                    level_type = getattr(grb, 'typeOfLevel', 'unknown')
                    level = getattr(grb, 'level', 0)
                    
                    # 时间信息
                    valid_date = getattr(grb, 'validDate', None)
                    valid_time = getattr(grb, 'validTime', None)
                    
                    # 收集信息
                    variables.add(f"{short_name}({name})")
                    levels.add(f"{level_type}={level}")
                    if valid_date and valid_time:
                        times.add(f"{valid_date}_{valid_time:04d}")
                    
                    # 详细输出前5个消息
                    if message_count <= 5:
                        print(f"\n消息 {message_count}:")
                        print(f"  变量: {short_name} ({name})")
                        print(f"  单位: {units}")
                        print(f"  层次类型: {level_type}")
                        print(f"  层次值: {level}")
                        print(f"  有效日期: {valid_date}")
                        print(f"  有效时间: {valid_time:04d}" if valid_time else "  有效时间: None")
                        
                        # 网格信息
                        try:
                            lats, lons = grb.latlons()
                            print(f"  网格尺寸: {lats.shape}")
                            print(f"  纬度范围: {lats.min():.3f} ~ {lats.max():.3f}")
                            print(f"  经度范围: {lons.min():.3f} ~ {lons.max():.3f}")
                        except:
                            print(f"  网格信息: 无法获取")
                
                print(f"\n总消息数: {message_count}")
                print(f"变量列表: {', '.join(sorted(variables))}")
                print(f"层次信息: {', '.join(sorted(levels))}")
                print(f"时间信息: {', '.join(sorted(times))}")
        
        except Exception as e:
            print(f"PYGRIB分析失败: {str(e)}")
            traceback.print_exc()
        
        try:
            # 使用xarray分析
            print(f"\n--- XARRAY/CFGRIB分析结果 ---")
            ds = xr.open_dataset(filepath, engine='cfgrib')
            
            print(f"数据集维度: {dict(ds.dims)}")
            print(f"坐标变量: {list(ds.coords.keys())}")
            print(f"数据变量: {list(ds.data_vars.keys())}")
            
            # 详细变量信息
            for var_name, var in ds.data_vars.items():
                print(f"\n变量: {var_name}")
                print(f"  维度: {var.dims}")
                print(f"  形状: {var.shape}")
                print(f"  数据类型: {var.dtype}")
                print(f"  属性: {dict(var.attrs)}")
            
            # 坐标信息
            for coord_name, coord in ds.coords.items():
                print(f"\n坐标: {coord_name}")
                print(f"  维度: {coord.dims}")
                print(f"  形状: {coord.shape}")
                print(f"  数据类型: {coord.dtype}")
                if coord.size <= 10:
                    print(f"  值: {coord.values}")
                else:
                    print(f"  范围: {coord.min().values} ~ {coord.max().values}")
            
            # 全局属性
            print(f"\n全局属性:")
            for attr, value in ds.attrs.items():
                print(f"  {attr}: {value}")
                
        except Exception as e:
            print(f"XARRAY分析失败: {str(e)}")
            traceback.print_exc()
        
        return file_info
    
    def compare_with_ecmwf_structure(self):
        """对比ECMWF处理结构的差异"""
        print(f"\n{'='*60}")
        print("HRCLDAS vs ECMWF 数据结构对比")
        print(f"{'='*60}")
        
        print("\n1. 文件命名规则:")
        print("   ECMWF: W_NAFP_C_ECMF_P_C1D_YYYYMMDDHHMM00_YYYYMMDDHHMM001_*")
        print("   HRCLDAS: Z_NAFP_C_BABJ_YYYYMMDDHHMMSS_P_HRCLDAS_RT_BENN_0P01_HOR-VAR-YYYYMMDDHH.GRB2")
        
        print("\n2. 时间信息:")
        print("   ECMWF: 包含参考时间和预报时间")
        print("   HRCLDAS: 包含文件创建时间和数据有效时间")
        
        print("\n3. 变量组织:")
        print("   ECMWF: 多变量单文件，按预报时效分类")
        print("   HRCLDAS: 单变量单文件，按小时实时更新")
        
        print("\n4. 空间分辨率:")
        print("   ECMWF: 通常较粗（如0.125°）")
        print("   HRCLDAS: 高分辨率（0.01°）")
        
        print("\n5. 数据来源:")
        print("   ECMWF: 欧洲中期天气预报中心")
        print("   HRCLDAS: 中国气象局高分辨率陆面数据同化系统")
    
    def generate_hrcldas_processor_template(self):
        """生成HRCLDAS数据处理器模板"""
        template = '''
class HRCLDASProcessor:
    """HRCLDAS数据处理器"""
    
    def __init__(self, log_file=None):
        self.logger = TimestampedLogger(log_file)
        
        # HRCLDAS变量映射
        self.HRCLDAS_VARS = {
            'QAIR': {'nc_name': 'specific_humidity', 'long_name': 'Specific Humidity', 'units': 'kg/kg'},
            'TAIR': {'nc_name': 'air_temperature', 'long_name': 'Air Temperature', 'units': 'K'},
            'PRES': {'nc_name': 'surface_pressure', 'long_name': 'Surface Pressure', 'units': 'Pa'},
            'WIND': {'nc_name': 'wind_speed', 'long_name': 'Wind Speed', 'units': 'm/s'},
            'PREC': {'nc_name': 'precipitation', 'long_name': 'Precipitation', 'units': 'mm/h'},
            # 添加更多变量...
        }
    
    def parse_hrcldas_filename(self, filename):
        """解析HRCLDAS文件名"""
        pattern = r'Z_NAFP_C_BABJ_(\\d{14})_P_HRCLDAS_RT_BENN_0P01_HOR-(\\w+)-(\\d{10})\\.GRB2?'
        match = re.match(pattern, filename)
        
        if match:
            creation_time = match.group(1)
            variable = match.group(2)
            data_time = match.group(3)
            
            return {
                'creation_time': datetime.strptime(creation_time, "%Y%m%d%H%M%S"),
                'data_time': datetime.strptime(data_time, "%Y%m%d%H"),
                'variable': variable
            }
        return None
    
    def process_hrcldas_file(self, filepath):
        """处理单个HRCLDAS文件"""
        file_info = self.parse_hrcldas_filename(Path(filepath).name)
        if not file_info:
            return None
            
        try:
            # 使用xarray读取
            ds = xr.open_dataset(filepath, engine='cfgrib')
            
            # 变量重命名
            var_name = file_info['variable']
            if var_name in self.HRCLDAS_VARS:
                var_info = self.HRCLDAS_VARS[var_name]
                # 重命名和添加属性...
            
            # 添加时间坐标
            ds.coords['data_time'] = file_info['data_time']
            ds.coords['creation_time'] = file_info['creation_time']
            
            return ds
            
        except Exception as e:
            print(f"处理文件失败: {filepath}, 错误: {str(e)}")
            return None
    
    def process_time_range(self, source_dir, output_dir, start_time, end_time, variables=None):
        """处理时间范围内的HRCLDAS数据"""
        # 实现时间范围处理逻辑...
        pass
'''
        
        print(f"\n{'='*60}")
        print("HRCLDAS处理器模板代码")
        print(f"{'='*60}")
        print(template)
        
        return template

def main():
    """主函数"""
    analyzer = HRCLDASAnalyzer()
    
    # 示例文件路径（请替换为实际路径）
    sample_file = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    print("HRCLDAS数据格式分析工具")
    print("="*60)
    
    # 如果文件存在则分析
    if os.path.exists(sample_file):
        analyzer.analyze_grib_file(sample_file)
    else:
        print(f"示例文件不存在: {sample_file}")
        print("请将实际的HRCLDAS文件路径传入analyzer.analyze_grib_file()方法")
    
    # 显示对比信息
    analyzer.compare_with_ecmwf_structure()
    
    # 生成处理器模板
    analyzer.generate_hrcldas_processor_template()
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
