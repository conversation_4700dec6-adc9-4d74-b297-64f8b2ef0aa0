{"cells": [{"cell_type": "code", "execution_count": 2, "id": "5622c7fb-37cb-4ea1-a848-902b687f037b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["===== ECMWF数据处理程序 =====\n", "源目录: /workspace/data/NAFP/EC_C1D\n", "GRIB目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n", "输出目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_nc\n", "时间范围: 202402191200-202402291200\n", "日志文件: /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/processing_log202402191200-202402291200.txt\n", "========================================\n", "\n", "\n", "==== 处理时间点: 202402191200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:19<00:00,  1.61it/s]\n", "解压进度: 100%|██████████| 32/32 [00:23<00:00,  1.37it/s]\n", "解压进度: 100%|██████████| 2/2 [00:12<00:00,  6.39s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 08:55:05] 解压文件        56.51s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 128.57it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 117.62it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 103.22it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 113.42it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 53.96it/s]\n", "重命名进度: 100%|██████████| 4/4 [00:00<00:00, 105.36it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 08:55:08] 文件更名        2.76s\t找到 164 个GRIB文件 | 成功更名 164/164 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "收集元数据: 100%|██████████| 32/32 [00:14<00:00,  2.18it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:08<00:00,  3.66it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.13s/it]\n", "处理文件: 100%|██████████| 32/32 [00:40<00:00,  1.28s/it]\n", "处理文件: 100%|██████████| 32/32 [00:29<00:00,  1.09it/s]\n", "处理文件: 100%|██████████| 2/2 [00:11<00:00,  5.69s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 08:56:59] 提取变量        111.37s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 09:06:41] 转存NC        581.90s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402191200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402191200_UPAR.NC\n", "\n", "==== 处理时间点: 202402200000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:22<00:00,  1.43it/s]\n", "解压进度: 100%|██████████| 32/32 [00:20<00:00,  1.55it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.67s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:07:43] 解压文件        60.47s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 43.03it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 58.83it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 45.70it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:07:48] 文件更名        5.09s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "收集元数据: 100%|██████████| 32/32 [00:13<00:00,  2.34it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:08<00:00,  3.91it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.02s/it]\n", "处理文件: 100%|██████████| 32/32 [01:04<00:00,  2.02s/it]\n", "处理文件: 100%|██████████| 32/32 [00:45<00:00,  1.41s/it]\n", "处理文件: 100%|██████████| 2/2 [00:11<00:00,  5.75s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:10:35] 提取变量        166.82s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 09:20:19] 转存NC        584.39s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402200000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402200000_UPAR.NC\n", "\n", "==== 处理时间点: 202402201200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:30<00:00,  1.04it/s]\n", "解压进度: 100%|██████████| 32/32 [00:28<00:00,  1.12it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.58s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:21:57] 解压文件        97.41s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 51.27it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 95.80it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 65.18it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:22:20] 文件更名        23.13s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:20<00:00,  1.57it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:18<00:00,  1.76it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.07s/it]\n", "处理文件: 100%|██████████| 32/32 [01:20<00:00,  2.52s/it]\n", "处理文件: 100%|██████████| 32/32 [00:40<00:00,  1.26s/it]\n", "处理文件: 100%|██████████| 2/2 [00:11<00:00,  5.52s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:26:15] 提取变量        234.64s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 09:36:02] 转存NC        586.68s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402201200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402201200_UPAR.NC\n", "\n", "==== 处理时间点: 202402210000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:25<00:00,  1.27it/s]\n", "解压进度: 100%|██████████| 32/32 [00:26<00:00,  1.19it/s]\n", "解压进度: 100%|██████████| 2/2 [00:12<00:00,  6.34s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:38:16] 解压文件        120.71s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 118.38it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 109.84it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 115.59it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:39:08] 文件更名        52.23s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:21<00:00,  1.52it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:21<00:00,  1.51it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.09s/it]\n", "处理文件: 100%|██████████| 32/32 [00:57<00:00,  1.78s/it]\n", "处理文件: 100%|██████████| 32/32 [00:54<00:00,  1.69s/it]\n", "处理文件: 100%|██████████| 2/2 [00:12<00:00,  6.06s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:43:31] 提取变量        263.41s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 09:53:32] 转存NC        600.50s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402210000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402210000_UPAR.NC\n", "\n", "==== 处理时间点: 202402211200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:23<00:00,  1.39it/s]\n", "解压进度: 100%|██████████| 32/32 [00:22<00:00,  1.44it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.71s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:55:21] 解压文件        108.69s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 77.71it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 101.06it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 68.74it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 09:56:05] 文件更名        44.43s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:12<00:00,  2.50it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:08<00:00,  3.76it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.07s/it]\n", "处理文件: 100%|██████████| 32/32 [01:08<00:00,  2.15s/it]\n", "处理文件: 100%|██████████| 32/32 [00:56<00:00,  1.75s/it]\n", "处理文件: 100%|██████████| 2/2 [00:11<00:00,  5.57s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:00:14] 提取变量        248.41s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 10:09:45] 转存NC        571.47s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402211200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402211200_UPAR.NC\n", "\n", "==== 处理时间点: 202402220000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:25<00:00,  1.26it/s]\n", "解压进度: 100%|██████████| 32/32 [00:27<00:00,  1.16it/s]\n", "解压进度: 100%|██████████| 2/2 [00:12<00:00,  6.06s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:11:45] 解压文件        118.47s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 97.07it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 118.86it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 85.43it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:12:40] 文件更名        55.17s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:23<00:00,  1.36it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:08<00:00,  3.84it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.06s/it]\n", "处理文件: 100%|██████████| 32/32 [01:27<00:00,  2.72s/it]\n", "处理文件: 100%|██████████| 32/32 [00:50<00:00,  1.59s/it]\n", "处理文件: 100%|██████████| 2/2 [00:11<00:00,  5.78s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:17:25] 提取变量        285.01s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 10:27:55] 转存NC        629.98s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402220000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402220000_UPAR.NC\n", "\n", "==== 处理时间点: 202402221200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:22<00:00,  1.39it/s]\n", "解压进度: 100%|██████████| 32/32 [00:19<00:00,  1.62it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.99s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:29:44] 解压文件        108.09s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 103.58it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 50.13it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 106.58it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:30:36] 文件更名        51.93s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:15<00:00,  2.05it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:08<00:00,  3.80it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:05<00:00,  2.54s/it]\n", "处理文件: 100%|██████████| 32/32 [01:27<00:00,  2.73s/it]\n", "处理文件: 100%|██████████| 32/32 [00:31<00:00,  1.01it/s]\n", "处理文件: 100%|██████████| 2/2 [00:12<00:00,  6.48s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:34:58] 提取变量        261.56s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 10:44:57] 转存NC        599.36s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402221200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402221200_UPAR.NC\n", "\n", "==== 处理时间点: 202402230000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:19<00:00,  1.67it/s]\n", "解压进度: 100%|██████████| 32/32 [00:23<00:00,  1.34it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.79s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:46:51] 解压文件        113.71s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 116.20it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 103.44it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 107.81it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:47:48] 文件更名        57.00s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:26<00:00,  1.21it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:22<00:00,  1.41it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:08<00:00,  4.41s/it]\n", "处理文件: 100%|██████████| 32/32 [01:26<00:00,  2.70s/it]\n", "处理文件: 100%|██████████| 32/32 [00:34<00:00,  1.09s/it]\n", "处理文件: 100%|██████████| 2/2 [00:12<00:00,  6.46s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 10:53:02] 提取变量        313.84s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 11:02:38] 转存NC        576.14s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402230000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402230000_UPAR.NC\n", "\n", "==== 处理时间点: 202402231200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:19<00:00,  1.62it/s]\n", "解压进度: 100%|██████████| 32/32 [00:20<00:00,  1.53it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.58s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:04:31] 解压文件        111.72s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 102.92it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 103.38it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 111.63it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:05:30] 文件更名        58.80s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:12<00:00,  2.59it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:08<00:00,  3.95it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.00s/it]\n", "处理文件: 100%|██████████| 32/32 [01:24<00:00,  2.64s/it]\n", "处理文件: 100%|██████████| 32/32 [00:35<00:00,  1.11s/it]\n", "处理文件: 100%|██████████| 2/2 [00:12<00:00,  6.28s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:10:04] 提取变量        274.29s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 11:19:30] 转存NC        565.49s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402231200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402231200_UPAR.NC\n", "\n", "==== 处理时间点: 202402240000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:19<00:00,  1.62it/s]\n", "解压进度: 100%|██████████| 32/32 [00:21<00:00,  1.49it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.65s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:21:25] 解压文件        114.16s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 65.83it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 113.03it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 109.78it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:22:26] 文件更名        60.90s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:12<00:00,  2.47it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:07<00:00,  4.12it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.09s/it]\n", "处理文件: 100%|██████████| 32/32 [01:05<00:00,  2.04s/it]\n", "处理文件: 100%|██████████| 32/32 [00:54<00:00,  1.71s/it]\n", "处理文件: 100%|██████████| 2/2 [00:11<00:00,  5.86s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:26:59] 提取变量        273.09s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 11:36:25] 转存NC        566.47s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402240000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402240000_UPAR.NC\n", "\n", "==== 处理时间点: 202402241200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:30<00:00,  1.05it/s]\n", "解压进度: 100%|██████████| 32/32 [00:21<00:00,  1.49it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.73s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:38:31] 解压文件        124.98s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 85.89it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 99.71it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 103.73it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:39:29] 文件更名        57.43s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:25<00:00,  1.25it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:07<00:00,  4.21it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:04<00:00,  2.20s/it]\n", "处理文件: 100%|██████████| 32/32 [01:01<00:00,  1.93s/it]\n", "处理文件: 100%|██████████| 32/32 [00:48<00:00,  1.51s/it]\n", "处理文件: 100%|██████████| 2/2 [00:10<00:00,  5.47s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:44:01] 提取变量        272.35s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 11:53:40] 转存NC        578.99s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402241200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402241200_UPAR.NC\n", "\n", "==== 处理时间点: 202402250000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:19<00:00,  1.65it/s]\n", "解压进度: 100%|██████████| 32/32 [00:20<00:00,  1.57it/s]\n", "解压进度: 100%|██████████| 2/2 [00:12<00:00,  6.18s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:55:25] 解压文件        103.57s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 117.93it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:04<00:00,  7.11it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 106.87it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 11:56:20] 文件更名        55.65s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:16<00:00,  1.90it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:11<00:00,  2.76it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:04<00:00,  2.11s/it]\n", "处理文件: 100%|██████████| 32/32 [01:29<00:00,  2.79s/it]\n", "处理文件: 100%|██████████| 32/32 [01:09<00:00,  2.17s/it]\n", "处理文件: 100%|██████████| 2/2 [00:13<00:00,  6.77s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:01:31] 提取变量        311.08s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 12:10:52] 转存NC        560.42s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402250000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402250000_UPAR.NC\n", "\n", "==== 处理时间点: 202402251200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:20<00:00,  1.53it/s]\n", "解压进度: 100%|██████████| 32/32 [00:27<00:00,  1.18it/s]\n", "解压进度: 100%|██████████| 2/2 [00:12<00:00,  6.21s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:13:04] 解压文件        131.41s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 132.22it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 94.37it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 77.35it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:14:16] 文件更名        71.75s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:13<00:00,  2.44it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:08<00:00,  3.89it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:03<00:00,  1.63s/it]\n", "处理文件: 100%|██████████| 32/32 [01:04<00:00,  2.00s/it]\n", "处理文件: 100%|██████████| 32/32 [00:39<00:00,  1.25s/it]\n", "处理文件: 100%|██████████| 2/2 [00:11<00:00,  5.71s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:19:04] 提取变量        287.91s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 12:28:01] 转存NC        537.65s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402251200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402251200_UPAR.NC\n", "\n", "==== 处理时间点: 202402260000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:20<00:00,  1.57it/s]\n", "解压进度: 100%|██████████| 32/32 [00:25<00:00,  1.26it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.86s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:29:51] 解压文件        105.32s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 126.27it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 123.47it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 79.32it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:30:37] 文件更名        45.62s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:33<00:00,  1.05s/it]\n", "收集元数据: 100%|██████████| 32/32 [00:08<00:00,  3.89it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.06s/it]\n", "处理文件: 100%|██████████| 32/32 [01:02<00:00,  1.95s/it]\n", "处理文件: 100%|██████████| 32/32 [00:37<00:00,  1.17s/it]\n", "处理文件: 100%|██████████| 2/2 [00:11<00:00,  5.82s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:34:54] 提取变量        257.76s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 12:43:54] 转存NC        539.35s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402260000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402260000_UPAR.NC\n", "\n", "==== 处理时间点: 202402261200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:19<00:00,  1.61it/s]\n", "解压进度: 100%|██████████| 32/32 [00:20<00:00,  1.53it/s]\n", "解压进度: 100%|██████████| 2/2 [00:09<00:00,  4.76s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:45:35] 解压文件        94.23s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 93.58it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 117.24it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 78.47it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:46:18] 文件更名        43.83s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:12<00:00,  2.56it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:09<00:00,  3.25it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.12s/it]\n", "处理文件: 100%|██████████| 32/32 [01:05<00:00,  2.05s/it]\n", "处理文件: 100%|██████████| 32/32 [00:35<00:00,  1.11s/it]\n", "处理文件: 100%|██████████| 2/2 [00:13<00:00,  6.61s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 12:50:09] 提取变量        230.40s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 12:59:07] 转存NC        538.63s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402261200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402261200_UPAR.NC\n", "\n", "==== 处理时间点: 202402270000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:22<00:00,  1.43it/s]\n", "解压进度: 100%|██████████| 32/32 [00:35<00:00,  1.09s/it]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.85s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:01:00] 解压文件        109.47s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 33.36it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 93.31it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 50.02it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:01:38] 文件更名        38.90s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:13<00:00,  2.32it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:08<00:00,  3.62it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.05s/it]\n", "处理文件: 100%|██████████| 32/32 [01:02<00:00,  1.95s/it]\n", "处理文件: 100%|██████████| 32/32 [00:44<00:00,  1.39s/it]\n", "处理文件: 100%|██████████| 2/2 [00:13<00:00,  6.56s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:05:28] 提取变量        229.67s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 13:14:28] 转存NC        540.09s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402270000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402270000_UPAR.NC\n", "\n", "==== 处理时间点: 202402271200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:32<00:00,  1.00s/it]\n", "解压进度: 100%|██████████| 32/32 [00:32<00:00,  1.01s/it]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.73s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:16:40] 解压文件        129.11s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 118.22it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 104.05it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 77.65it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:17:31] 文件更名        50.81s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:26<00:00,  1.20it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:10<00:00,  3.12it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.45s/it]\n", "处理文件: 100%|██████████| 32/32 [01:04<00:00,  2.02s/it]\n", "处理文件: 100%|██████████| 32/32 [00:37<00:00,  1.18s/it]\n", "处理文件: 100%|██████████| 2/2 [00:12<00:00,  6.48s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:21:51] 提取变量        260.53s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 13:30:49] 转存NC        537.79s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402271200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402271200_UPAR.NC\n", "\n", "==== 处理时间点: 202402280000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:22<00:00,  1.42it/s]\n", "解压进度: 100%|██████████| 32/32 [00:19<00:00,  1.65it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.84s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:32:31] 解压文件        100.87s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 71.20it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 119.97it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 73.84it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:33:17] 文件更名        46.14s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:14<00:00,  2.21it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:10<00:00,  2.95it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.27s/it]\n", "处理文件: 100%|██████████| 32/32 [00:58<00:00,  1.83s/it]\n", "处理文件: 100%|██████████| 32/32 [00:39<00:00,  1.22s/it]\n", "处理文件: 100%|██████████| 2/2 [00:13<00:00,  6.58s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:37:18] 提取变量        240.41s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 13:46:18] 转存NC        540.68s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402280000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402280000_UPAR.NC\n", "\n", "==== 处理时间点: 202402281200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:21<00:00,  1.46it/s]\n", "解压进度: 100%|██████████| 32/32 [00:20<00:00,  1.60it/s]\n", "解压进度: 100%|██████████| 2/2 [00:12<00:00,  6.11s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:48:09] 解压文件        109.36s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 108.35it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 124.05it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 101.54it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:49:05] 文件更名        55.89s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:14<00:00,  2.19it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:09<00:00,  3.43it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.14s/it]\n", "处理文件: 100%|██████████| 32/32 [01:34<00:00,  2.96s/it]\n", "处理文件: 100%|██████████| 32/32 [00:37<00:00,  1.17s/it]\n", "处理文件: 100%|██████████| 2/2 [00:13<00:00,  6.52s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 13:53:59] 提取变量        294.43s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 14:03:20] 转存NC        560.87s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402281200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402281200_UPAR.NC\n", "\n", "==== 处理时间点: 202402290000 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:27<00:00,  1.15it/s]\n", "解压进度: 100%|██████████| 32/32 [00:25<00:00,  1.24it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.89s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 14:05:30] 解压文件        128.35s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 121.99it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 116.81it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 111.03it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 14:06:32] 文件更名        62.16s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:12<00:00,  2.63it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:16<00:00,  1.95it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:04<00:00,  2.30s/it]\n", "处理文件: 100%|██████████| 32/32 [01:29<00:00,  2.81s/it]\n", "处理文件: 100%|██████████| 32/32 [00:39<00:00,  1.25s/it]\n", "处理文件: 100%|██████████| 2/2 [00:12<00:00,  6.42s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 14:11:30] 提取变量        297.99s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 14:20:58] 转存NC        567.74s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402290000_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402290000_UPAR.NC\n", "\n", "==== 处理时间点: 202402291200 ====\n"]}, {"name": "stderr", "output_type": "stream", "text": ["解压进度: 100%|██████████| 32/32 [00:25<00:00,  1.24it/s]\n", "解压进度: 100%|██████████| 32/32 [00:19<00:00,  1.66it/s]\n", "解压进度: 100%|██████████| 2/2 [00:13<00:00,  6.82s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 14:23:00] 解压文件        121.13s\t找到 66 个.bz2文件 | 成功解压 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["重命名进度: 100%|██████████| 32/32 [00:00<00:00, 89.30it/s]\n", "重命名进度: 100%|██████████| 32/32 [00:00<00:00, 118.72it/s]\n", "重命名进度: 100%|██████████| 2/2 [00:00<00:00, 77.92it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 14:24:02] 文件更名        62.11s\t找到 66 个GRIB文件 | 成功更名 66/66 个文件 | 目录: /workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\n"]}, {"name": "stderr", "output_type": "stream", "text": ["收集元数据: 100%|██████████| 32/32 [00:13<00:00,  2.40it/s]\n", "收集元数据: 100%|██████████| 32/32 [00:12<00:00,  2.56it/s]\n", "收集元数据: 100%|██████████| 2/2 [00:02<00:00,  1.07s/it]\n", "处理文件: 100%|██████████| 32/32 [01:28<00:00,  2.77s/it]\n", "处理文件: 100%|██████████| 32/32 [00:53<00:00,  1.66s/it]\n", "处理文件: 100%|██████████| 2/2 [00:12<00:00,  6.45s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2025-06-27 14:29:12] 提取变量        309.36s\t高空变量: d, gh, pv, q, r, t, u, v, w | 地面变量: al, cape, capes, cp, d2m, deg0l, fal, fg310, fzra, lcc, lsp, mn2t3, mn2t6, msl, mx2t3, mx2t6, p10fg6, ptype, rsn, sd, sf, skt, sp, sst, t2m, tcc, tcw, tcwv, tp, u10, u100, v10, v100, vis, z\n", "[2025-06-27 14:38:47] 转存NC        575.48s\tSURF.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402291200_SURF.NC | UPAR.NC -> /workspace/EC/TEMP/EC_C1D/EC_C1D_nc/NAFP_ECMF_C1D_GXDSJ_202402291200_UPAR.NC\n", "\n", "===== 处理完成 =====\n"]}], "source": ["import os\n", "import bz2\n", "import shutil\n", "import re\n", "import glob\n", "import warnings\n", "import traceback\n", "from datetime import datetime, timedelta\n", "from collections import defaultdict\n", "import xarray as xr\n", "import numpy as np\n", "from pathlib import Path\n", "import logging\n", "import pygrib\n", "from tqdm import tqdm\n", "from multiprocessing import Pool, cpu_count\n", "import functools\n", "import sys\n", "import time\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=FutureWarning)\n", "# 配置日志和警告\n", "logging.getLogger('cfgrib').setLevel(logging.ERROR)\n", "logging.getLogger('eccodes').setLevel(logging.ERROR)\n", "logging.getLogger('cfgrib').setLevel(logging.CRITICAL)\n", "warnings.filterwarnings(\"ignore\", category=RuntimeWarning, module=\"cfgrib\")\n", "\n", "class TimestampedLogger:\n", "    \"\"\"带时间戳的日志记录器(简化版)\"\"\"\n", "    def __init__(self, log_file=None):\n", "        self.log_file = log_file\n", "        if log_file:\n", "            os.makedirs(os.path.dirname(log_file), exist_ok=True)\n", "            with open(log_file, 'w', encoding='utf-8') as f:  # 清空现有日志文件\n", "                f.write(\"\")\n", "        self.operation_start_time = {}\n", "\n", "    def log_header(self, source_dir, grib_dir, output_dir, time_range):\n", "        \"\"\"记录头部信息\"\"\"\n", "        message = f\"\"\"===== ECMWF数据处理程序 =====\n", "源目录: {source_dir}\n", "GRIB目录: {grib_dir}\n", "输出目录: {output_dir}\n", "时间范围: {time_range}\n", "日志文件: {self.log_file if self.log_file else '无'}\n", "{\"=\" * 40}\\n\"\"\"\n", "        print(message)\n", "        if self.log_file:\n", "            with open(self.log_file, 'a', encoding='utf-8') as f:\n", "                f.write(message)\n", "\n", "    def start_operation(self, operation_name):\n", "        \"\"\"记录操作开始时间\"\"\"\n", "        self.operation_start_time[operation_name] = time.time()\n", "\n", "    def log_operation(self, operation_name, status_message):\n", "        \"\"\"记录操作结果\"\"\"\n", "        if operation_name not in self.operation_start_time:\n", "            self.start_operation(operation_name)\n", "        \n", "        elapsed = time.time() - self.operation_start_time[operation_name]\n", "        \n", "        timestamp = datetime.now().strftime(\"[%Y-%m-%d %H:%M:%S]\")\n", "        message = f\"{timestamp} {operation_name.ljust(12)}{elapsed:.2f}s\\t{status_message}\"\n", "        \n", "        print(message)\n", "        if self.log_file:\n", "            with open(self.log_file, 'a', encoding='utf-8') as f:\n", "                f.write(message + \"\\n\")\n", "        \n", "        # 删除已记录的操作时间\n", "        if operation_name in self.operation_start_time:\n", "            del self.operation_start_time[operation_name]\n", "\n", "    def log_footer(self, success=True):\n", "        \"\"\"记录结束信息\"\"\"\n", "        message = \"\\n===== 处理完成 =====\"\n", "        if not success:\n", "            message += \"\\n===== 处理过程中出现错误 =====\"\n", "        print(message)\n", "        if self.log_file:\n", "            with open(self.log_file, 'a', encoding='utf-8') as f:\n", "                f.write(message + \"\\n\")\n", "\n", "class ECMWFProcessor:\n", "    \"\"\"优化后的ECMWF数据处理全流程封装\"\"\"\n", "\n", "    def __init__(self, log_file=None):\n", "        # 初始化日志记录器\n", "        self.logger = TimestampedLogger(log_file)\n", "        \n", "        # 定义常量\n", "        # 第一类地面变量\n", "        self.TYPE1_SURFACE_VARS = [\n", "            '100u', '100v', '10fg3', '10fg6', '10u', '10v', '2d', '2t', 'cape', 'capes', \n", "            'cp', 'deg0l', 'fal', 'fzra', 'lcc', 'lsp', 'mn2t3', 'mn2t6', 'msl',\n", "            'mx2t3', 'mx2t6', 'ptype', 'rsn', 'sd', 'sf', 'skt', 'sp', 'sst', 'tcc',\n", "            'tcw', 'tcwv', 'tp', 'vis'\n", "        ]\n", "        # 第二类地面变量\n", "        self.TYPE2_SURFACE_VARS = [\n", "            '100u', '100v', '10fg6', '10u', '10v', '2d', '2t', 'cape', 'capes', 'cp', \n", "            'deg0l', 'fal', 'fzra', 'lcc', 'lsp', 'mn2t6', 'msl', 'mx2t6', 'ptype',\n", "            'rsn', 'sd', 'sf', 'skt', 'sp', 'sst', 'tcc', 'tcw', 'tcwv', 'tp', 'vis'\n", "        ]\n", "        # 第三类地面变量\n", "        self.TYPE3_SURFACE_VARS = [\n", "            '100u', '100v', '10fg3', '10u', '10v', '2d', '2t', 'cape', 'capes', 'cp', \n", "            'deg0l', 'fal', 'fzra', 'lcc', 'lsp', 'mn2t3', 'msl', 'mx2t3', 'ptype',\n", "            'rsn', 'sd', 'sf', 'skt', 'sp', 'sst', 'tcc', 'tcw', 'tcwv', 'tp', 'vis'\n", "        ]\n", "        # 第四类地面变量\n", "        self.TYPE4_SURFACE_VARS = ['10fg3', 'mn2t3', 'mx2t3']\n", "        # 第五类地面变量 (更新为只有al和z)\n", "        self.TYPE5_SURFACE_VARS = ['al', 'z']\n", "        # 第六类地面变量 (新增的特殊1分钟文件)\n", "        self.TYPE6_SURFACE_VARS = [\n", "            '100u', '100v', '10u', '10v', '2d', '2t', 'cape', 'capes', 'cp', 'deg0l', \n", "            'fal', 'fzra', 'lcc', 'lsp', 'msl', 'ptype', 'rsn', 'sd', 'sf', 'skt', \n", "            'sp', 'sst', 'tcc', 'tcw', 'tcwv', 'tp', 'vis'\n", "        ]\n", "        \n", "        # 高空变量\n", "        self.UPPER_VARS = ['v', 'u', 'd', 't', 'q', 'gh', 'pv', 'r', 'w']\n", "        \n", "        # 变量名映射\n", "        self.GRIB_TO_NC_VAR_MAP = {\n", "            '100u': 'u100',\n", "            '100v': 'v100',\n", "            '10u': 'u10',\n", "            '10v': 'v10',\n", "            '10fg3': 'fg310',\n", "            '10fg6': 'p10fg6',\n", "            '2d': 'd2m',\n", "            '2t': 't2m',\n", "            'cape': 'cape',\n", "            'capes': 'capes',\n", "            'cp': 'cp',\n", "            'deg0l': 'deg0l',\n", "            'fal': 'fal',\n", "            'fzra': 'fzra',\n", "            'lcc': 'lcc',\n", "            'lsp': 'lsp',\n", "            'mn2t3': 'mn2t3',\n", "            'mn2t6': 'mn2t6',\n", "            'msl': 'msl',\n", "            'mx2t3': 'mx2t3',\n", "            'mx2t6': 'mx2t6',\n", "            'ptype': 'ptype',\n", "            'rsn': 'rsn',\n", "            'sd': 'sd',\n", "            'sf': 'sf',\n", "            'skt': 'skt',\n", "            'sp': 'sp',\n", "            'sst': 'sst',\n", "            'tcc': 'tcc',\n", "            'tcw': 'tcw',\n", "            'tcwv': 'tcwv',\n", "            'tp': 'tp',\n", "            'vis': 'vis',\n", "            'al': 'al',\n", "            'z': 'z'\n", "        }\n", "        \n", "        # 预报时效分类\n", "        self.TYPE1_FORECAST_HOURS = [\n", "            6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 66, 72, 78, 84, 90, 96, \n", "            102, 108, 114, 120, 126, 132, 138, 144\n", "        ]\n", "        self.TYPE2_FORECAST_HOURS = [\n", "            150, 156, 162, 168, 174, 180, 186, 192, 198, 204, 210, 216, 222, 228, 234, 240\n", "        ]\n", "        self.TYPE3_FORECAST_HOURS = [\n", "            3, 9, 15, 21, 27, 33, 39, 45, 51, 57, 63, 69\n", "        ]\n", "        self.TYPE4_FORECAST_HOURS = [\n", "            75, 81, 87, 93, 99, 105, 111, 117, 123, 129, 135, 141\n", "        ]\n", "        self.TYPE5_FORECAST_HOURS = [0]\n", "        self.TYPE6_FORECAST_HOURS = [0]  # 预报时效也为0，但文件名格式不同\n", "        \n", "        # 缓存变量元数据\n", "        self.metadata_cache = {}\n", "        # Windows下最大进程数限制（避免句柄超过63）\n", "        self.MAX_WORKERS = min(32, max(1, cpu_count() - 2))\n", "\n", "    def decompress_files(self, source_subdir, target_dir, reference_time):\n", "        \"\"\"并行解压.bz2文件\"\"\"\n", "        self.logger.start_operation(\"解压文件\")\n", "        os.makedirs(target_dir, exist_ok=True)\n", "        \n", "        # 直接匹配目录下所有.bz2文件\n", "        bz2_files = glob.glob(os.path.join(source_subdir, \"*.bz2\"))\n", "        \n", "        if not bz2_files:\n", "            status = f\"警告: 在 {source_subdir} 中没有找到任何.bz2文件\"\n", "            self.logger.log_operation(\"解压文件\", status)\n", "            return False\n", "        \n", "        # 准备解压任务\n", "        target_files = []\n", "        for bz2_file in bz2_files:\n", "            filename = os.path.basename(bz2_file)\n", "            target_path = os.path.join(target_dir, filename[:-4])  # 去掉.bz2扩展名\n", "            target_files.append((bz2_file, target_path))\n", "        \n", "        # 分批处理，每批不超过MAX_WORKERS个文件\n", "        success = 0\n", "        chunks = self._chunk_files(target_files, self.MAX_WORKERS)\n", "        \n", "        for chunk in chunks:\n", "            with Pool(processes=len(chunk)) as pool:\n", "                results = list(tqdm(\n", "                    pool.imap(self._decompress_single_file, chunk),\n", "                    total=len(chunk),\n", "                    desc=\"解压进度\",\n", "                    dynamic_ncols=True\n", "                ))\n", "            success += sum(results)\n", "        \n", "        status = f\"找到 {len(bz2_files)} 个.bz2文件 | 成功解压 {success}/{len(target_files)} 个文件 | 目录: {target_dir}\"\n", "        self.logger.log_operation(\"解压文件\", status)\n", "        return success > 0\n", "\n", "\n", "    def _chunk_files(self, files, chunk_size):\n", "        \"\"\"将文件列表分块，每块不超过chunk_size\"\"\"\n", "        return [files[i:i + chunk_size] for i in range(0, len(files), chunk_size)]\n", "\n", "    def _decompress_single_file(self, file_pair):\n", "        \"\"\"解压单个文件（用于并行处理）\"\"\"\n", "        source_path, target_path = file_pair\n", "        try:\n", "            with bz2.BZ2File(source_path, 'rb') as f_in:\n", "                with open(target_path, 'wb') as f_out:\n", "                    shutil.copyfileobj(f_in, f_out)\n", "            self._clean_index_files(target_path)\n", "            return True\n", "        except Exception as e:\n", "            traceback.print_exc()\n", "            return False\n", "\n", "    def rename_files(self, source_dir, reference_time):\n", "        \"\"\"批量重命名文件\"\"\"\n", "        self.logger.start_operation(\"文件更名\")\n", "        \n", "        try:\n", "            # 收集所有.bz2解压后的文件（无扩展名）\n", "            grib_files = glob.glob(os.path.join(source_dir, \"W_NAFP_C_ECMF_*_P_C1D*\"))\n", "            \n", "            if not grib_files:\n", "                status = \"提示: 没有找到需要重命名的GRIB文件\"\n", "                self.logger.log_operation(\"文件更名\", status)\n", "                return True\n", "            \n", "            rename_tasks = []\n", "            for old_path in grib_files:\n", "                new_name = self._generate_new_name(old_path, reference_time)\n", "                if new_name:\n", "                    new_path = os.path.join(source_dir, new_name)\n", "                    rename_tasks.append((old_path, new_path))\n", "            \n", "            if not rename_tasks:\n", "                status = \"提示: 没有需要重命名的文件（可能文件已正确命名）\"\n", "                self.logger.log_operation(\"文件更名\", status)\n", "                return True\n", "            \n", "            # 分批并行重命名\n", "            success_count = 0\n", "            chunks = self._chunk_files(rename_tasks, self.MAX_WORKERS)\n", "            \n", "            for chunk in chunks:\n", "                with Pool(processes=len(chunk)) as pool:\n", "                    results = list(tqdm(\n", "                        pool.imap(self._rename_single_file, chunk),\n", "                        total=len(chunk),\n", "                        desc=\"重命名进度\",\n", "                        dynamic_ncols=True\n", "                    ))\n", "                success_count += sum(results)\n", "            \n", "            status = f\"找到 {len(grib_files)} 个GRIB文件 | 成功更名 {success_count}/{len(rename_tasks)} 个文件 | 目录: {source_dir}\"\n", "            self.logger.log_operation(\"文件更名\", status)\n", "            return True\n", "        \n", "        except Exception as e:\n", "            traceback.print_exc()\n", "            return False\n", "\n", "\n", "    def _rename_single_file(self, rename_pair):\n", "        \"\"\"重命名单个文件（用于并行处理）\"\"\"\n", "        old_path, new_path = rename_pair\n", "        try:\n", "            self._clean_index_files(old_path)\n", "            \n", "            # 检查目标文件是否存在，存在则删除\n", "            if os.path.exists(new_path):\n", "                try:\n", "                    os.remove(new_path)\n", "                    self._clean_index_files(new_path)\n", "                except Exception:\n", "                    return False\n", "            \n", "            os.rename(old_path, new_path)\n", "            self._clean_index_files(new_path)\n", "            return True\n", "        except Exception:\n", "            return True\n", "\n", "    def convert_grib_to_nc(self, input_dir, output_dir, reference_time):\n", "        \"\"\"并行转换GRIB文件为NetCDF (修复变量冲突版本)\"\"\"\n", "        self.logger.start_operation(\"提取变量\")\n", "        \n", "        os.makedirs(output_dir, exist_ok=True)\n", "        \n", "        try:\n", "            # 分类文件\n", "            type1_files, type2_files, type3_files, type4_files, type5_files, type6_files = self._classify_grib_files(input_dir, reference_time)\n", "            all_files = type1_files + type2_files + type3_files + type4_files + type5_files + type6_files\n", "            \n", "            if not all_files:\n", "                status = \"错误: 未找到任何匹配的GRIB文件！\"\n", "                self.logger.log_operation(\"提取变量\", status)\n", "                return False\n", "            \n", "            # 处理文件\n", "            ref_dt = datetime.strptime(reference_time, \"%Y%m%d%H%M\")\n", "            ref_str = ref_dt.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "            \n", "            # 预处理：收集所有元数据（分批处理）\n", "            metadata = {}\n", "            chunks = self._chunk_files(all_files, self.MAX_WORKERS)\n", "            \n", "            for chunk in chunks:\n", "                with Pool(processes=len(chunk)) as pool:\n", "                    metadata_list = list(tqdm(\n", "                        pool.imap(self._get_grib_metadata, chunk),\n", "                        total=len(chunk),\n", "                        desc=\"收集元数据\",\n", "                        dynamic_ncols=True\n", "                    ))\n", "                \n", "                # 合并元数据\n", "                for md in metadata_list:\n", "                    if md:\n", "                        metadata.update(md)\n", "            \n", "            # 并行处理文件（分批处理）\n", "            results = []\n", "            chunks = self._chunk_files(all_files, self.MAX_WORKERS)\n", "            \n", "            for chunk in chunks:\n", "                with Pool(processes=len(chunk)) as pool:\n", "                    chunk_results = list(tqdm(\n", "                        pool.imap(functools.partial(\n", "                            self._process_single_grib_file_parallel, \n", "                            ref_time=ref_dt\n", "                        ), chunk),\n", "                        total=len(chunk),\n", "                        desc=\"处理文件\",\n", "                        dynamic_ncols=True\n", "                    ))\n", "                results.extend([r for r in chunk_results if r is not None])\n", "            \n", "            # 组织结果 - 按预报时次分类\n", "            surface_data = defaultdict(list)\n", "            upper_data = defaultdict(list)\n", "            \n", "            for s_ds, u_ds, forecast_hour in results:\n", "                if s_ds is not None:\n", "                    surface_data[forecast_hour].append(s_ds)\n", "                if u_ds is not None:\n", "                    upper_data[forecast_hour].append(u_ds)\n", "            \n", "            # 获取变量列表用于日志记录\n", "            surface_vars = set()\n", "            upper_vars = set()\n", "            for ds_list in surface_data.values():\n", "                for ds in ds_list:\n", "                    surface_vars.update(ds.data_vars.keys())\n", "            for ds_list in upper_data.values():\n", "                for ds in ds_list:\n", "                    upper_vars.update(ds.data_vars.keys())\n", "            \n", "            status = f\"高空变量: {', '.join(sorted(upper_vars))} | 地面变量: {', '.join(sorted(surface_vars))}\"\n", "            self.logger.log_operation(\"提取变量\", status)\n", "            \n", "            # 保存结果\n", "            output_prefix = f\"NAFP_ECMF_C1D_GXDSJ_{ref_dt.strftime('%Y%m%d%H')}00\"\n", "            has_output = False\n", "            \n", "            self.logger.start_operation(\"转存NC\")\n", "            \n", "            # 处理地面数据\n", "            if surface_data:\n", "                output_path = os.path.join(output_dir, f\"{output_prefix}_SURF.NC\")\n", "                try:\n", "                    # 对每个预报时次的数据先合并，再按时间维度拼接\n", "                    surface_datasets = []\n", "                    for hour in sorted(surface_data.keys()):\n", "                        merged = xr.merge(surface_data[hour], compat='override')\n", "                        surface_datasets.append(merged)\n", "                    \n", "                    if surface_datasets:\n", "                        combined = xr.concat(surface_datasets, dim='forecast_time')\n", "                        combined.coords['reference_time'] = np.datetime64(ref_str, 'ns')\n", "                        combined.attrs.update({\n", "                            'modename': 'NAFP_ECMF_C1D',\n", "                            'description': 'Data sourced from GXQXJ'\n", "                        })\n", "\n", "                        # 添加元数据\n", "                        for var in combined.data_vars:\n", "                            if var in metadata:\n", "                                combined[var].attrs.update({\n", "                                    'long_name': metadata[var]['long_name'],\n", "                                    'units': metadata[var]['units']\n", "                                })\n", "                        \n", "                        # 设置编码并保存\n", "                        encoding = {\n", "                            var: {\n", "                                'zlib': True,\n", "                                'complevel': 1,\n", "                                'dtype': 'float32',\n", "                                '_FillValue': None,\n", "                                'chunksizes': self._get_chunksizes(combined[var])\n", "                            } for var in combined.data_vars\n", "                        }\n", "                        encoding.update({\n", "                            'forecast_time': {'dtype': 'int32', '_FillValue': None},\n", "                            'reference_time': {'dtype': 'int64', '_FillValue': None}\n", "                        })\n", "                        \n", "                        combined.to_netcdf(output_path, encoding=encoding)\n", "                        has_output = True\n", "                except Exception as e:\n", "                    traceback.print_exc()\n", "            \n", "            # 处理高空数据\n", "            if upper_data:\n", "                output_path = os.path.join(output_dir, f\"{output_prefix}_UPAR.NC\")\n", "                try:\n", "                    # 对每个预报时次的数据先合并，再按时间维度拼接\n", "                    upper_datasets = []\n", "                    for hour in sorted(upper_data.keys()):\n", "                        merged = xr.merge(upper_data[hour], compat='override')\n", "                        upper_datasets.append(merged)\n", "                    \n", "                    if upper_datasets:\n", "                        combined = xr.concat(upper_datasets, dim='forecast_time')\n", "                        combined.coords['reference_time'] = np.datetime64(ref_str, 'ns')\n", "                        combined.attrs.update({\n", "                            'modename': 'NAFP_ECMF_C1D',\n", "                            'description': 'Data sourced from GXQXJ'\n", "                        })\n", "                        # 添加元数据\n", "                        for var in combined.data_vars:\n", "                            if var in metadata:\n", "                                combined[var].attrs.update({\n", "                                    'long_name': metadata[var]['long_name'],\n", "                                    'units': metadata[var]['units']\n", "                                })\n", "                        \n", "                        # 设置编码并保存\n", "                        encoding = {\n", "                            var: {\n", "                                'zlib': True,\n", "                                'complevel': 1,\n", "                                'dtype': 'float32',\n", "                                '_FillValue': None,\n", "                                'chunksizes': self._get_chunksizes(combined[var])\n", "                            } for var in combined.data_vars\n", "                        }\n", "                        encoding.update({\n", "                            'forecast_time': {'dtype': 'int32', '_FillValue': None},\n", "                            'reference_time': {'dtype': 'int64', '_FillValue': None}\n", "                        })\n", "                        \n", "                        combined.to_netcdf(output_path, encoding=encoding)\n", "                        has_output = True\n", "                except Exception as e:\n", "                    traceback.print_exc()\n", "            \n", "            status = f\"SURF.NC -> {os.path.join(output_dir, f'{output_prefix}_SURF.NC')} | UPAR.NC -> {os.path.join(output_dir, f'{output_prefix}_UPAR.NC')}\"\n", "            self.logger.log_operation(\"转存NC\", status)\n", "            \n", "            return has_output\n", "        \n", "        except Exception as e:\n", "            traceback.print_exc()\n", "            return False\n", "\n", "\n", "    def _process_single_grib_file_parallel(self, grib_file, ref_time):\n", "        \"\"\"并行处理单个GRIB文件\"\"\"\n", "        try:\n", "            time_info = self._parse_time_from_filename(grib_file)\n", "            forecast_hour = time_info['forecast_hours']\n", "            file_type = time_info['file_type']\n", "            \n", "            surface_ds, upper_ds = self._process_single_grib_file(grib_file, file_type)\n", "            \n", "            if surface_ds is not None:\n", "                surface_ds.coords['forecast_time'] = ref_time + timedelta(hours=forecast_hour)\n", "            \n", "            if upper_ds is not None:\n", "                upper_ds.coords['forecast_time'] = ref_time + timedelta(hours=forecast_hour)\n", "            \n", "            return surface_ds, upper_ds, forecast_hour\n", "        except Exception as e:\n", "            print(f\"处理文件 {grib_file} 时出错: {str(e)}\")\n", "            traceback.print_exc()\n", "            return None, None, None\n", "        finally:\n", "            self._clean_index_files(grib_file)\n", "\n", "    def process_time_range(self, source_dir, grib_dir, output_dir, time_range_str):\n", "        \"\"\"优化后的时间段处理\"\"\"\n", "        try:\n", "            start_str, end_str = time_range_str.split('-')\n", "            start_time = datetime.strptime(start_str, \"%Y%m%d%H%M\")\n", "            end_time = datetime.strptime(end_str, \"%Y%m%d%H%M\")\n", "            \n", "            # 记录头部信息\n", "            self.logger.log_header(source_dir, grib_dir, output_dir, time_range_str)\n", "            \n", "            current_time = start_time\n", "            while current_time <= end_time:\n", "                reference_time = current_time.strftime(\"%Y%m%d%H%M\")\n", "                year_str = current_time.strftime(\"%Y\")\n", "                ref_subdir = current_time.strftime(\"%Y%m%d%H\")\n", "                \n", "                print(f\"\\n==== 处理时间点: {reference_time} ====\")\n", "                if self.logger.log_file:\n", "                    with open(self.logger.log_file, 'a', encoding='utf-8') as f:\n", "                        f.write(f\"\\n==== 处理时间点: {reference_time} ====\\n\")\n", "                \n", "                # 构建正确的源子目录路径（包含年份子目录）\n", "                source_subdir_path = os.path.join(source_dir, year_str, ref_subdir)\n", "                if not os.path.exists(source_subdir_path):\n", "                    print(f\"警告: 源子目录 {source_subdir_path} 不存在\")\n", "                    if self.logger.log_file:\n", "                        with open(self.logger.log_file, 'a', encoding='utf-8') as f:\n", "                            f.write(f\"警告: 源子目录 {source_subdir_path} 不存在\\n\")\n", "                    current_time += <PERSON><PERSON>ta(hours=12)\n", "                    continue\n", "                \n", "                # 1. 解压文件\n", "                decompress_success = self.decompress_files(source_subdir_path, grib_dir, reference_time)\n", "                if not decompress_success:\n", "                    current_time += <PERSON><PERSON>ta(hours=12)\n", "                    continue\n", "                \n", "                # 2. 重命名文件\n", "                rename_success = self.rename_files(grib_dir, reference_time)\n", "                if not rename_success:\n", "                    pass  # 即使重命名失败也继续处理\n", "                \n", "                # 3. 转换文件\n", "                convert_success = self.convert_grib_to_nc(grib_dir, output_dir, reference_time)\n", "                if not convert_success:\n", "                    pass  # 即使转换失败也继续处理\n", "                \n", "                # 4. 清理临时文件\n", "                try:\n", "                    self._clean_directory(grib_dir)\n", "                except Exception as e:\n", "                    traceback.print_exc()\n", "                \n", "                current_time += <PERSON><PERSON>ta(hours=12)\n", "            \n", "            self.logger.log_footer(True)\n", "            return True\n", "        except Exception as e:\n", "            self.logger.log_footer(False)\n", "            traceback.print_exc()\n", "            return False\n", "\n", "\n", "    # ------------------------- 内部方法 -------------------------\n", "    \n", "    def _generate_new_name(self, filepath, reference_time):\n", "        \"\"\"生成新文件名（修复日期解析版本）\"\"\"\n", "        filename = Path(filepath).name\n", "        \n", "        # 尝试匹配TYPE6的特殊1分钟文件\n", "        type6_match = re.match(r'W_NAFP_C_ECMF_\\d+_P_C1D(\\d{8})(\\d{8})1', filename)\n", "        if type6_match:\n", "            ref_mdhm = type6_match.group(1)  # MMDDHHMM\n", "            fcst_mdhm = type6_match.group(2) # MMDDHHMM\n", "            \n", "            # 解析日期时间（添加年份）\n", "            ref_year = int(reference_time[:4])\n", "            try:\n", "                ref_dt = datetime.strptime(f\"{ref_year}{ref_mdhm}\", \"%Y%m%d%H%M\")\n", "                fcst_dt = datetime.strptime(f\"{ref_year}{fcst_mdhm}\", \"%Y%m%d%H%M\")\n", "                \n", "                # 检查是否为TYPE6文件（1分钟差）\n", "                if (fcst_dt - ref_dt) == timedelta(minutes=1):\n", "                    extra_info = filename.split('_')[3]  # 获取原文件名中的额外信息\n", "                    new_name = (\n", "                        f\"W_NAFP_C_ECMF_P_C1D_\"\n", "                        f\"{ref_dt.strftime('%Y%m%d%H%M')}00_\"\n", "                        f\"{fcst_dt.strftime('%Y%m%d%H%M')}001\"\n", "                        f\"_{extra_info}\"\n", "                        f\"{Path(filepath).suffix}\"\n", "                    )\n", "                    return new_name\n", "            except ValueError:\n", "                pass\n", "        \n", "        # 处理常规文件\n", "        match = re.match(r'W_NAFP_C_ECMF_\\d+_P_C1D(\\d{8})(\\d{8})1', filename)\n", "        if not match:\n", "            return None\n", "        \n", "        # 获取原文件名中的额外信息部分\n", "        extra_info = filename.split('_')[3]\n", "        \n", "        ref_mdhm = match.group(1)  # MMDDHHMM\n", "        fcst_mdhm = match.group(2) # MMDDHHMM\n", "        \n", "        # 添加年份信息\n", "        ref_year = int(reference_time[:4])\n", "        try:\n", "            ref_dt = datetime.strptime(f\"{ref_year}{ref_mdhm}\", \"%Y%m%d%H%M\")\n", "            fcst_dt = datetime.strptime(f\"{ref_year}{fcst_mdhm}\", \"%Y%m%d%H%M\")\n", "        except ValueError as e:\n", "            print(f\"日期解析错误: {e} | 文件名: {filename}\")\n", "            return None\n", "        \n", "        new_name = (\n", "            f\"W_NAFP_C_ECMF_P_C1D_\"\n", "            f\"{ref_dt.strftime('%Y%m%d%H%M')}00_\"\n", "            f\"{fcst_dt.strftime('%Y%m%d%H%M')}001\"\n", "            f\"_{extra_info}\"\n", "            f\"{Path(filepath).suffix}\"\n", "        )\n", "        return new_name\n", "\n", "\n", "    def _classify_grib_files(self, directory, reference_time):\n", "        \"\"\"分类GRIB文件\"\"\"\n", "        ref_dt = datetime.strptime(reference_time, \"%Y%m%d%H%M\")\n", "        \n", "        # 第一类文件\n", "        type1_patterns = [\n", "            f\"W_NAFP_C_ECMF_P_C1D_{reference_time}00_\"\n", "            f\"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*\"\n", "            for h in self.TYPE1_FORECAST_HOURS\n", "        ]\n", "        \n", "        # 第二类文件\n", "        type2_patterns = [\n", "            f\"W_NAFP_C_ECMF_P_C1D_{reference_time}00_\"\n", "            f\"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*\"\n", "            for h in self.TYPE2_FORECAST_HOURS\n", "        ]\n", "        \n", "        # 第三类文件\n", "        type3_patterns = [\n", "            f\"W_NAFP_C_ECMF_P_C1D_{reference_time}00_\"\n", "            f\"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*\"\n", "            for h in self.TYPE3_FORECAST_HOURS\n", "        ]\n", "        \n", "        # 第四类文件\n", "        type4_patterns = [\n", "            f\"W_NAFP_C_ECMF_P_C1D_{reference_time}00_\"\n", "            f\"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*\"\n", "            for h in self.TYPE4_FORECAST_HOURS\n", "        ]\n", "        \n", "        # 第五类文件\n", "        type5_patterns = [\n", "            f\"W_NAFP_C_ECMF_P_C1D_{reference_time}00_\"\n", "            f\"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*\"\n", "            for h in self.TYPE5_FORECAST_HOURS\n", "        ]\n", "        \n", "        # 第六类文件 (特殊1分钟文件)\n", "        type6_patterns = [\n", "            f\"W_NAFP_C_ECMF_P_C1D_{reference_time}00_\"\n", "            f\"{(ref_dt + <PERSON><PERSON><PERSON>(minutes=1)).strftime('%Y%m%d%H%M')}001_*\"\n", "        ]\n", "        \n", "        type1_files = self._find_matching_files(directory, type1_patterns)\n", "        type2_files = self._find_matching_files(directory, type2_patterns)\n", "        type3_files = self._find_matching_files(directory, type3_patterns)\n", "        type4_files = self._find_matching_files(directory, type4_patterns)\n", "        type5_files = self._find_matching_files(directory, type5_patterns)\n", "        type6_files = self._find_matching_files(directory, type6_patterns)\n", "        \n", "        return type1_files, type2_files, type3_files, type4_files, type5_files, type6_files\n", "\n", "    def _find_matching_files(self, directory, patterns):\n", "        \"\"\"查找匹配模式的文件\"\"\"\n", "        matched = []\n", "        for p in patterns:\n", "            found_files = glob.glob(os.path.join(directory, p))\n", "            matched.extend(found_files)\n", "        return sorted(matched)\n", "\n", "    def _process_single_grib_file(self, grib_file, file_type):\n", "        \"\"\"处理单个GRIB文件\"\"\"\n", "        # 检测可用气压层\n", "        pressure_levels = set()\n", "        for edition in [1, 2]:\n", "            try:\n", "                with xr.open_dataset(grib_file, engine=\"cfgrib\", \n", "                                    backend_kwargs={'filter_by_keys': {'edition': edition}}) as ds:\n", "                    if 'isobaricInhPa' in ds.coords:\n", "                        pressure_levels.update(ds.isobaricInhPa.values)\n", "            except Exception:\n", "                continue\n", "        \n", "        # 根据文件类型确定需要提取的地面变量\n", "        if file_type == 1:\n", "            surface_vars = self.TYPE1_SURFACE_VARS\n", "        elif file_type == 2:\n", "            surface_vars = self.TYPE2_SURFACE_VARS\n", "        elif file_type == 3:\n", "            surface_vars = self.TYPE3_SURFACE_VARS\n", "        elif file_type == 4:\n", "            surface_vars = self.TYPE4_SURFACE_VARS\n", "        elif file_type == 5:\n", "            surface_vars = self.TYPE5_SURFACE_VARS\n", "        elif file_type == 6:\n", "            surface_vars = self.TYPE6_SURFACE_VARS\n", "        else:\n", "            surface_vars = []  # 未知类型的文件不提取地面变量\n", "        \n", "        # 并行读取数据\n", "        surface_ds = []\n", "        upper_ds = []\n", "        \n", "        for edition in [1, 2]:\n", "            # 地面数据\n", "            try:\n", "                ds = xr.open_dataset(\n", "                    grib_file,\n", "                    engine=\"cfgrib\",\n", "                    backend_kwargs={\n", "                        'filter_by_keys': {\n", "                            'edition': edition,\n", "                            'typeOfLevel': ['surface', 'meanSea', 'heightAboveGround', \n", "                            'heightAboveGroundLayer', 'cloudBase', 'cloudTop']\n", "                        }\n", "                    }\n", "                )\n", "                if ds.data_vars:\n", "                    # 只保留当前类型需要的地面变量\n", "                    vars_to_keep = list(ds.data_vars.keys())\n", "                    if vars_to_keep:\n", "                        ds = ds[vars_to_keep].expand_dims({'level': [0]})\n", "                        surface_ds.append(ds)\n", "            except Exception as e:\n", "                continue\n", "            \n", "            # 高空数据 - 处理所有预报时次\n", "            for level in sorted(pressure_levels, reverse=True):\n", "                try:\n", "                    ds = xr.open_dataset(\n", "                        grib_file,\n", "                        engine=\"cfgrib\",\n", "                        backend_kwargs={\n", "                            'filter_by_keys': {\n", "                                'edition': edition,\n", "                                'typeOfLevel': 'isobaricInhPa',\n", "                                'level': level\n", "                            }\n", "                        }\n", "                    )\n", "                    if ds.data_vars:\n", "                        # 保留所有高空变量\n", "                        vars_to_keep = [var for var in ds.data_vars if var in self.UPPER_VARS]\n", "                        if vars_to_keep:\n", "                            ds = ds[vars_to_keep].expand_dims({'isobaricInhPa': [level]})\n", "                            upper_ds.append(ds)\n", "                except Exception:\n", "                    continue\n", "        \n", "        # 合并数据\n", "        combined_surface = xr.merge(surface_ds) if surface_ds else None\n", "        combined_upper = xr.concat(upper_ds, dim='isobaricInhPa') if upper_ds else None\n", "        \n", "        # 只对映射表中的变量进行重命名，其他变量保持不变\n", "        if combined_surface is not None:\n", "            rename_dict = {}\n", "            for var in combined_surface.data_vars:\n", "                if var in self.GRIB_TO_NC_VAR_MAP:\n", "                    rename_dict[var] = self.GRIB_TO_NC_VAR_MAP[var]\n", "            if rename_dict:\n", "                combined_surface = combined_surface.rename(rename_dict)\n", "        \n", "        if combined_upper is not None:\n", "            rename_dict = {}\n", "            for var in combined_upper.data_vars:\n", "                if var in self.GRIB_TO_NC_VAR_MAP:\n", "                    rename_dict[var] = self.GRIB_TO_NC_VAR_MAP[var]\n", "            if rename_dict:\n", "                combined_upper = combined_upper.rename(rename_dict)\n", "        \n", "        return combined_surface, combined_upper\n", "\n", "    def _parse_time_from_filename(self, filename):\n", "        \"\"\"从文件名解析时间信息（修复日期解析版本）\"\"\"\n", "        filename = Path(filename).stem\n", "        \n", "        # 新文件名格式: W_NAFP_C_ECMF_P_C1D_YYYYMMDDHHMM00_YYYYMMDDHHMM001_*\n", "        new_pattern = re.compile(\n", "            r'W_NAFP_C_ECMF_P_C1D_'\n", "            r'(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})00_'  # 参考时间\n", "            r'(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})001'  # 预报时间\n", "        )\n", "        \n", "        match = new_pattern.search(filename)\n", "        if match:\n", "            # 从新格式文件名中提取时间\n", "            ref_year = int(match.group(1))\n", "            ref_month = int(match.group(2))\n", "            ref_day = int(match.group(3))\n", "            ref_hour = int(match.group(4))\n", "            ref_minute = int(match.group(5))\n", "            \n", "            fcst_year = int(match.group(6))\n", "            fcst_month = int(match.group(7))\n", "            fcst_day = int(match.group(8))\n", "            fcst_hour = int(match.group(9))\n", "            fcst_minute = int(match.group(10))\n", "            \n", "            ref_time = datetime(ref_year, ref_month, ref_day, ref_hour, ref_minute)\n", "            fcst_time = datetime(fcst_year, fcst_month, fcst_day, fcst_hour, fcst_minute)\n", "            \n", "            time_diff = fcst_time - ref_time\n", "            \n", "            # 确定文件类型\n", "            file_type = self._determine_file_type(time_diff)\n", "            \n", "            return {\n", "                'reference_time': ref_time,\n", "                'forecast_time': fcst_time,\n", "                'forecast_hours': int(time_diff.total_seconds() / 3600),\n", "                'file_type': file_type\n", "            }\n", "        \n", "        # 旧文件名格式: W_NAFP_C_ECMF_*_P_C1DMMDDHHMMMMDDHHMM1\n", "        old_pattern = re.compile(r'W_NAFP_C_ECMF_\\d+_P_C1D(\\d{8})(\\d{8})1')\n", "        match = old_pattern.search(filename)\n", "        if match:\n", "            ref_mdhm = match.group(1)  # MMDDHHMM\n", "            fcst_mdhm = match.group(2) # MMDDHHMM\n", "            \n", "            # 从reference_time获取年份\n", "            ref_year = int(filename.split('_')[5][:4]) if '_' in filename else int(datetime.now().year)\n", "            \n", "            try:\n", "                ref_dt = datetime.strptime(f\"{ref_year}{ref_mdhm}\", \"%Y%m%d%H%M\")\n", "                fcst_dt = datetime.strptime(f\"{ref_year}{fcst_mdhm}\", \"%Y%m%d%H%M\")\n", "                \n", "                time_diff = fcst_dt - ref_dt\n", "                file_type = self._determine_file_type(time_diff)\n", "                \n", "                return {\n", "                    'reference_time': ref_dt,\n", "                    'forecast_time': fcst_dt,\n", "                    'forecast_hours': int(time_diff.total_seconds() / 3600),\n", "                    'file_type': file_type\n", "                }\n", "            except ValueError as e:\n", "                print(f\"日期解析错误: {e} | 文件名: {filename}\")\n", "                raise ValueError(f\"无法从文件名 {filename} 中解析时间信息\")\n", "    \n", "        raise ValueError(f\"无法识别文件名格式: {filename}\")\n", "    \n", "    def _get_grib_metadata(self, grib_file):\n", "        \"\"\"从GRIB文件中提取变量元数据\"\"\"\n", "        metadata = {}\n", "        try:\n", "            with pygrib.open(grib_file) as grbs:\n", "                for grb in grbs:\n", "                    short_name = grb.shortName\n", "                    # 双向检查映射关系\n", "                    if short_name in self.GRIB_TO_NC_VAR_MAP:\n", "                        nc_name = self.GRIB_TO_NC_VAR_MAP[short_name]\n", "                    elif short_name in self.GRIB_TO_NC_VAR_MAP.values():\n", "                        nc_name = short_name\n", "                    else:\n", "                        continue\n", "                    \n", "                    if nc_name not in metadata:\n", "                        metadata[nc_name] = {\n", "                            'long_name': grb.name,\n", "                            'units': grb.units\n", "                        }\n", "        except Exception:\n", "            pass\n", "        return metadata\n", "\n", "    def _clean_index_files(self, file_path):\n", "        \"\"\"清理临时索引文件\"\"\"\n", "        try:\n", "            idx_file = Path(file_path).with_suffix('.idx')\n", "            if idx_file.exists():\n", "                idx_file.unlink()\n", "        except Exception:\n", "            pass\n", "\n", "    def _clean_directory(self, directory):\n", "        \"\"\"清理目录中的所有索引文件\"\"\"\n", "        for root, _, files in os.walk(directory):\n", "            for file in files:\n", "                if file.endswith('.idx'):\n", "                    try:\n", "                        os.remove(os.path.join(root, file))\n", "                    except Exception:\n", "                        pass\n", "\n", "    def _save_combined_data(self, datasets, output_path, data_type, reference_time, metadata):\n", "        \"\"\"保存合并后的数据\"\"\"\n", "        if not datasets:\n", "            return\n", "        \n", "        try:\n", "            # 修改这里：添加 compat='override' 参数来处理变量冲突\n", "            combined = xr.concat([xr.merge(ds_list, compat='override') for ds_list in datasets], \n", "                               dim='forecast_time')\n", "            \n", "            ref_time_ns = np.datetime64(reference_time, 'ns')\n", "            combined.coords['reference_time'] = ref_time_ns\n", "            \n", "            # 添加变量元数据\n", "            for var in combined.data_vars:\n", "                if var in metadata:\n", "                    combined[var].attrs.update({\n", "                        'long_name': metadata[var]['long_name'],\n", "                        'units': metadata[var]['units']\n", "                    })\n", "            \n", "            # 添加自定义全局属性\n", "            combined.attrs.update({\n", "                'modename': 'NAFP_ECMF_C1D',\n", "                'description': 'Data sourced from GXQXJ'\n", "            })\n", "            \n", "            # 设置编码\n", "            encoding = {\n", "                var: {\n", "                    'zlib': True,\n", "                    'complevel': 1, \n", "                    'dtype': 'float32',\n", "                    '_FillValue': None,\n", "                    'chunksizes': self._get_chunksizes(combined[var])\n", "                } for var in combined.data_vars\n", "            }\n", "            encoding.update({\n", "                'forecast_time': {'dtype': 'int32', '_FillValue': None},\n", "                'reference_time': {'dtype': 'int64', '_FillValue': None}\n", "            })\n", "            \n", "            combined.to_netcdf(output_path, encoding=encoding)\n", "        except Exception as e:\n", "            raise Exception(f\"保存数据失败: {str(e)}\")\n", "            \n", "    def _determine_file_type(self, time_diff):\n", "        \"\"\"根据时间差确定文件类型\"\"\"\n", "        hours = time_diff.total_seconds() / 3600\n", "        \n", "        if time_diff == timedelta(minutes=1):\n", "            return 6  # 特殊1分钟文件\n", "        elif hours in self.TYPE1_FORECAST_HOURS:\n", "            return 1\n", "        elif hours in self.TYPE2_FORECAST_HOURS:\n", "            return 2\n", "        elif hours in self.TYPE3_FORECAST_HOURS:\n", "            return 3\n", "        elif hours in self.TYPE4_FORECAST_HOURS:\n", "            return 4\n", "        elif hours in self.TYPE5_FORECAST_HOURS:\n", "            return 5\n", "        else:\n", "            return 0  # 未知类型\n", "\n", "    def _get_chunksizes(self, var):\n", "        \"\"\"计算变量的合适chunk大小\"\"\"\n", "        chunks = []\n", "        for dim in var.dims:\n", "            size = len(var[dim])\n", "            if dim == 'isobaricInhPa':\n", "                chunks.append(min(4, size))\n", "            elif dim == 'latitude':\n", "                chunks.append(min(128, size))\n", "            elif dim == 'longitude':\n", "                chunks.append(min(256, size))\n", "            elif dim == 'forecast_time':\n", "                chunks.append(1)\n", "            else:\n", "                chunks.append(size)\n", "        return tuple(chunks)\n", "\n", "if __name__ == \"__main__\":\n", "    # ===== 用户配置区域 =====\n", "    # 配置处理参数（修改这些变量即可）\n", "    SOURCE_DIR = \"/workspace/data/NAFP/EC_C1D\"       # 原始.bz2文件目录\n", "    GRIB_DIR = \"/workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200\"    # 解压后的GRIB文件目录\n", "    OUTPUT_DIR = \"/workspace/EC/TEMP/EC_C1D/EC_C1D_nc\"    # 输出NetCDF文件目录\n", "    TIME_RANGE = \"202402191200-202402291200\"             # 处理时间范围 201810111200\n", "    LOG_FILE = os.path.join(OUTPUT_DIR, \"processing_log202402191200-202402291200.txt\")  # 日志文件路径\n", "\n", "    # ===== 执行处理 =====\n", "    processor = ECMWFProcessor(LOG_FILE)\n", "    \n", "    # 确保目录存在\n", "    os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "    os.makedirs(GRIB_DIR, exist_ok=True)\n", "    \n", "    # 创建处理器实例并运行\n", "    success = processor.process_time_range(SOURCE_DIR, GRIB_DIR, OUTPUT_DIR, TIME_RANGE)\n", "    \n", "    if not success:\n", "        sys.exit(1)\n"]}, {"cell_type": "code", "execution_count": null, "id": "d0101968-4001-4264-94d2-f9c9a71550cb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}