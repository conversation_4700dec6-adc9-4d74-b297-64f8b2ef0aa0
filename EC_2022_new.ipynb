import os
import bz2
import shutil
import re
import glob
import warnings
import traceback
from datetime import datetime, timedelta
from collections import defaultdict
import xarray as xr
import numpy as np
from pathlib import Path
import logging
import pygrib
from tqdm import tqdm
from multiprocessing import Pool, cpu_count
import functools
import sys
import time
import warnings
warnings.filterwarnings("ignore", category=FutureWarning)
# 配置日志和警告
logging.getLogger('cfgrib').setLevel(logging.ERROR)
logging.getLogger('eccodes').setLevel(logging.ERROR)
logging.getLogger('cfgrib').setLevel(logging.CRITICAL)
warnings.filterwarnings("ignore", category=RuntimeWarning, module="cfgrib")

class TimestampedLogger:
    """带时间戳的日志记录器(简化版)"""
    def __init__(self, log_file=None):
        self.log_file = log_file
        if log_file:
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            with open(log_file, 'w', encoding='utf-8') as f:  # 清空现有日志文件
                f.write("")
        self.operation_start_time = {}

    def log_header(self, source_dir, grib_dir, output_dir, time_range):
        """记录头部信息"""
        message = f"""===== ECMWF数据处理程序 =====
源目录: {source_dir}
GRIB目录: {grib_dir}
输出目录: {output_dir}
时间范围: {time_range}
日志文件: {self.log_file if self.log_file else '无'}
{"=" * 40}\n"""
        print(message)
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message)

    def start_operation(self, operation_name):
        """记录操作开始时间"""
        self.operation_start_time[operation_name] = time.time()

    def log_operation(self, operation_name, status_message):
        """记录操作结果"""
        if operation_name not in self.operation_start_time:
            self.start_operation(operation_name)
        
        elapsed = time.time() - self.operation_start_time[operation_name]
        
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
        message = f"{timestamp} {operation_name.ljust(12)}{elapsed:.2f}s\t{status_message}"
        
        print(message)
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message + "\n")
        
        # 删除已记录的操作时间
        if operation_name in self.operation_start_time:
            del self.operation_start_time[operation_name]

    def log_footer(self, success=True):
        """记录结束信息"""
        message = "\n===== 处理完成 ====="
        if not success:
            message += "\n===== 处理过程中出现错误 ====="
        print(message)
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message + "\n")

class ECMWFProcessor:
    """优化后的ECMWF数据处理全流程封装"""

    def __init__(self, log_file=None):
        # 初始化日志记录器
        self.logger = TimestampedLogger(log_file)
        
        # 定义常量
        # 第一类地面变量
        self.TYPE1_SURFACE_VARS = [
            '100u', '100v', '10fg3', '10fg6', '10u', '10v', '2d', '2t', 'cape', 'capes', 
            'cp', 'deg0l', 'fal', 'fzra', 'lcc', 'lsp', 'mn2t3', 'mn2t6', 'msl',
            'mx2t3', 'mx2t6', 'ptype', 'rsn', 'sd', 'sf', 'skt', 'sp', 'sst', 'tcc',
            'tcw', 'tcwv', 'tp', 'vis'
        ]
        # 第二类地面变量
        self.TYPE2_SURFACE_VARS = [
            '100u', '100v', '10fg6', '10u', '10v', '2d', '2t', 'cape', 'capes', 'cp', 
            'deg0l', 'fal', 'fzra', 'lcc', 'lsp', 'mn2t6', 'msl', 'mx2t6', 'ptype',
            'rsn', 'sd', 'sf', 'skt', 'sp', 'sst', 'tcc', 'tcw', 'tcwv', 'tp', 'vis'
        ]
        # 第三类地面变量
        self.TYPE3_SURFACE_VARS = [
            '100u', '100v', '10fg3', '10u', '10v', '2d', '2t', 'cape', 'capes', 'cp', 
            'deg0l', 'fal', 'fzra', 'lcc', 'lsp', 'mn2t3', 'msl', 'mx2t3', 'ptype',
            'rsn', 'sd', 'sf', 'skt', 'sp', 'sst', 'tcc', 'tcw', 'tcwv', 'tp', 'vis'
        ]
        # 第四类地面变量
        self.TYPE4_SURFACE_VARS = ['10fg3', 'mn2t3', 'mx2t3']
        # 第五类地面变量 (更新为只有al和z)
        self.TYPE5_SURFACE_VARS = ['al', 'z']
        # 第六类地面变量 (新增的特殊1分钟文件)
        self.TYPE6_SURFACE_VARS = [
            '100u', '100v', '10u', '10v', '2d', '2t', 'cape', 'capes', 'cp', 'deg0l', 
            'fal', 'fzra', 'lcc', 'lsp', 'msl', 'ptype', 'rsn', 'sd', 'sf', 'skt', 
            'sp', 'sst', 'tcc', 'tcw', 'tcwv', 'tp', 'vis'
        ]
        
        # 高空变量
        self.UPPER_VARS = ['v', 'u', 'd', 't', 'q', 'gh', 'pv', 'r', 'w']
        
        # 变量名映射
        self.GRIB_TO_NC_VAR_MAP = {
            '100u': 'u100',
            '100v': 'v100',
            '10u': 'u10',
            '10v': 'v10',
            '10fg3': 'fg310',
            '10fg6': 'p10fg6',
            '2d': 'd2m',
            '2t': 't2m',
            'cape': 'cape',
            'capes': 'capes',
            'cp': 'cp',
            'deg0l': 'deg0l',
            'fal': 'fal',
            'fzra': 'fzra',
            'lcc': 'lcc',
            'lsp': 'lsp',
            'mn2t3': 'mn2t3',
            'mn2t6': 'mn2t6',
            'msl': 'msl',
            'mx2t3': 'mx2t3',
            'mx2t6': 'mx2t6',
            'ptype': 'ptype',
            'rsn': 'rsn',
            'sd': 'sd',
            'sf': 'sf',
            'skt': 'skt',
            'sp': 'sp',
            'sst': 'sst',
            'tcc': 'tcc',
            'tcw': 'tcw',
            'tcwv': 'tcwv',
            'tp': 'tp',
            'vis': 'vis',
            'al': 'al',
            'z': 'z'
        }
        
        # 预报时效分类
        self.TYPE1_FORECAST_HOURS = [
            6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 66, 72, 78, 84, 90, 96, 
            102, 108, 114, 120, 126, 132, 138, 144
        ]
        self.TYPE2_FORECAST_HOURS = [
            150, 156, 162, 168, 174, 180, 186, 192, 198, 204, 210, 216, 222, 228, 234, 240
        ]
        self.TYPE3_FORECAST_HOURS = [
            3, 9, 15, 21, 27, 33, 39, 45, 51, 57, 63, 69
        ]
        self.TYPE4_FORECAST_HOURS = [
            75, 81, 87, 93, 99, 105, 111, 117, 123, 129, 135, 141
        ]
        self.TYPE5_FORECAST_HOURS = [0]
        self.TYPE6_FORECAST_HOURS = [0]  # 预报时效也为0，但文件名格式不同
        
        # 缓存变量元数据
        self.metadata_cache = {}
        # Windows下最大进程数限制（避免句柄超过63）
        self.MAX_WORKERS = min(32, max(1, cpu_count() - 2))

    def decompress_files(self, source_subdir, target_dir, reference_time):
        """并行解压.bz2文件"""
        self.logger.start_operation("解压文件")
        os.makedirs(target_dir, exist_ok=True)
        
        # 直接匹配目录下所有.bz2文件
        bz2_files = glob.glob(os.path.join(source_subdir, "*.bz2"))
        
        if not bz2_files:
            status = f"警告: 在 {source_subdir} 中没有找到任何.bz2文件"
            self.logger.log_operation("解压文件", status)
            return False
        
        # 准备解压任务
        target_files = []
        for bz2_file in bz2_files:
            filename = os.path.basename(bz2_file)
            target_path = os.path.join(target_dir, filename[:-4])  # 去掉.bz2扩展名
            target_files.append((bz2_file, target_path))
        
        # 分批处理，每批不超过MAX_WORKERS个文件
        success = 0
        chunks = self._chunk_files(target_files, self.MAX_WORKERS)
        
        for chunk in chunks:
            with Pool(processes=len(chunk)) as pool:
                results = list(tqdm(
                    pool.imap(self._decompress_single_file, chunk),
                    total=len(chunk),
                    desc="解压进度",
                    dynamic_ncols=True
                ))
            success += sum(results)
        
        status = f"找到 {len(bz2_files)} 个.bz2文件 | 成功解压 {success}/{len(target_files)} 个文件 | 目录: {target_dir}"
        self.logger.log_operation("解压文件", status)
        return success > 0


    def _chunk_files(self, files, chunk_size):
        """将文件列表分块，每块不超过chunk_size"""
        return [files[i:i + chunk_size] for i in range(0, len(files), chunk_size)]

    def _decompress_single_file(self, file_pair):
        """解压单个文件（用于并行处理）"""
        source_path, target_path = file_pair
        try:
            with bz2.BZ2File(source_path, 'rb') as f_in:
                with open(target_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            self._clean_index_files(target_path)
            return True
        except Exception as e:
            traceback.print_exc()
            return False

    def rename_files(self, source_dir, reference_time):
        """批量重命名文件"""
        self.logger.start_operation("文件更名")
        
        try:
            # 收集所有.bz2解压后的文件（无扩展名）
            grib_files = glob.glob(os.path.join(source_dir, "W_NAFP_C_ECMF_*_P_C1D*"))
            
            if not grib_files:
                status = "提示: 没有找到需要重命名的GRIB文件"
                self.logger.log_operation("文件更名", status)
                return True
            
            rename_tasks = []
            for old_path in grib_files:
                new_name = self._generate_new_name(old_path, reference_time)
                if new_name:
                    new_path = os.path.join(source_dir, new_name)
                    rename_tasks.append((old_path, new_path))
            
            if not rename_tasks:
                status = "提示: 没有需要重命名的文件（可能文件已正确命名）"
                self.logger.log_operation("文件更名", status)
                return True
            
            # 分批并行重命名
            success_count = 0
            chunks = self._chunk_files(rename_tasks, self.MAX_WORKERS)
            
            for chunk in chunks:
                with Pool(processes=len(chunk)) as pool:
                    results = list(tqdm(
                        pool.imap(self._rename_single_file, chunk),
                        total=len(chunk),
                        desc="重命名进度",
                        dynamic_ncols=True
                    ))
                success_count += sum(results)
            
            status = f"找到 {len(grib_files)} 个GRIB文件 | 成功更名 {success_count}/{len(rename_tasks)} 个文件 | 目录: {source_dir}"
            self.logger.log_operation("文件更名", status)
            return True
        
        except Exception as e:
            traceback.print_exc()
            return False


    def _rename_single_file(self, rename_pair):
        """重命名单个文件（用于并行处理）"""
        old_path, new_path = rename_pair
        try:
            self._clean_index_files(old_path)
            
            # 检查目标文件是否存在，存在则删除
            if os.path.exists(new_path):
                try:
                    os.remove(new_path)
                    self._clean_index_files(new_path)
                except Exception:
                    return False
            
            os.rename(old_path, new_path)
            self._clean_index_files(new_path)
            return True
        except Exception:
            return True

    def convert_grib_to_nc(self, input_dir, output_dir, reference_time):
        """并行转换GRIB文件为NetCDF (修复变量冲突版本)"""
        self.logger.start_operation("提取变量")
        
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # 分类文件
            type1_files, type2_files, type3_files, type4_files, type5_files, type6_files = self._classify_grib_files(input_dir, reference_time)
            all_files = type1_files + type2_files + type3_files + type4_files + type5_files + type6_files
            
            if not all_files:
                status = "错误: 未找到任何匹配的GRIB文件！"
                self.logger.log_operation("提取变量", status)
                return False
            
            # 处理文件
            ref_dt = datetime.strptime(reference_time, "%Y%m%d%H%M")
            ref_str = ref_dt.strftime("%Y-%m-%d %H:%M:%S")
            
            # 预处理：收集所有元数据（分批处理）
            metadata = {}
            chunks = self._chunk_files(all_files, self.MAX_WORKERS)
            
            for chunk in chunks:
                with Pool(processes=len(chunk)) as pool:
                    metadata_list = list(tqdm(
                        pool.imap(self._get_grib_metadata, chunk),
                        total=len(chunk),
                        desc="收集元数据",
                        dynamic_ncols=True
                    ))
                
                # 合并元数据
                for md in metadata_list:
                    if md:
                        metadata.update(md)
            
            # 并行处理文件（分批处理）
            results = []
            chunks = self._chunk_files(all_files, self.MAX_WORKERS)
            
            for chunk in chunks:
                with Pool(processes=len(chunk)) as pool:
                    chunk_results = list(tqdm(
                        pool.imap(functools.partial(
                            self._process_single_grib_file_parallel, 
                            ref_time=ref_dt
                        ), chunk),
                        total=len(chunk),
                        desc="处理文件",
                        dynamic_ncols=True
                    ))
                results.extend([r for r in chunk_results if r is not None])
            
            # 组织结果 - 按预报时次分类
            surface_data = defaultdict(list)
            upper_data = defaultdict(list)
            
            for s_ds, u_ds, forecast_hour in results:
                if s_ds is not None:
                    surface_data[forecast_hour].append(s_ds)
                if u_ds is not None:
                    upper_data[forecast_hour].append(u_ds)
            
            # 获取变量列表用于日志记录
            surface_vars = set()
            upper_vars = set()
            for ds_list in surface_data.values():
                for ds in ds_list:
                    surface_vars.update(ds.data_vars.keys())
            for ds_list in upper_data.values():
                for ds in ds_list:
                    upper_vars.update(ds.data_vars.keys())
            
            status = f"高空变量: {', '.join(sorted(upper_vars))} | 地面变量: {', '.join(sorted(surface_vars))}"
            self.logger.log_operation("提取变量", status)
            
            # 保存结果
            output_prefix = f"NAFP_ECMF_C1D_GXDSJ_{ref_dt.strftime('%Y%m%d%H')}00"
            has_output = False
            
            self.logger.start_operation("转存NC")
            
            # 处理地面数据
            if surface_data:
                output_path = os.path.join(output_dir, f"{output_prefix}_SURF.NC")
                try:
                    # 对每个预报时次的数据先合并，再按时间维度拼接
                    surface_datasets = []
                    for hour in sorted(surface_data.keys()):
                        merged = xr.merge(surface_data[hour], compat='override')
                        surface_datasets.append(merged)
                    
                    if surface_datasets:
                        combined = xr.concat(surface_datasets, dim='forecast_time')
                        combined.coords['reference_time'] = np.datetime64(ref_str, 'ns')
                        combined.attrs.update({
                            'modename': 'NAFP_ECMF_C1D',
                            'description': 'Data sourced from GXQXJ'
                        })

                        # 添加元数据
                        for var in combined.data_vars:
                            if var in metadata:
                                combined[var].attrs.update({
                                    'long_name': metadata[var]['long_name'],
                                    'units': metadata[var]['units']
                                })
                        
                        # 设置编码并保存
                        encoding = {
                            var: {
                                'zlib': True,
                                'complevel': 1,
                                'dtype': 'float32',
                                '_FillValue': None,
                                'chunksizes': self._get_chunksizes(combined[var])
                            } for var in combined.data_vars
                        }
                        encoding.update({
                            'forecast_time': {'dtype': 'int32', '_FillValue': None},
                            'reference_time': {'dtype': 'int64', '_FillValue': None}
                        })
                        
                        combined.to_netcdf(output_path, encoding=encoding)
                        has_output = True
                except Exception as e:
                    traceback.print_exc()
            
            # 处理高空数据
            if upper_data:
                output_path = os.path.join(output_dir, f"{output_prefix}_UPAR.NC")
                try:
                    # 对每个预报时次的数据先合并，再按时间维度拼接
                    upper_datasets = []
                    for hour in sorted(upper_data.keys()):
                        merged = xr.merge(upper_data[hour], compat='override')
                        upper_datasets.append(merged)
                    
                    if upper_datasets:
                        combined = xr.concat(upper_datasets, dim='forecast_time')
                        combined.coords['reference_time'] = np.datetime64(ref_str, 'ns')
                        combined.attrs.update({
                            'modename': 'NAFP_ECMF_C1D',
                            'description': 'Data sourced from GXQXJ'
                        })
                        # 添加元数据
                        for var in combined.data_vars:
                            if var in metadata:
                                combined[var].attrs.update({
                                    'long_name': metadata[var]['long_name'],
                                    'units': metadata[var]['units']
                                })
                        
                        # 设置编码并保存
                        encoding = {
                            var: {
                                'zlib': True,
                                'complevel': 1,
                                'dtype': 'float32',
                                '_FillValue': None,
                                'chunksizes': self._get_chunksizes(combined[var])
                            } for var in combined.data_vars
                        }
                        encoding.update({
                            'forecast_time': {'dtype': 'int32', '_FillValue': None},
                            'reference_time': {'dtype': 'int64', '_FillValue': None}
                        })
                        
                        combined.to_netcdf(output_path, encoding=encoding)
                        has_output = True
                except Exception as e:
                    traceback.print_exc()
            
            status = f"SURF.NC -> {os.path.join(output_dir, f'{output_prefix}_SURF.NC')} | UPAR.NC -> {os.path.join(output_dir, f'{output_prefix}_UPAR.NC')}"
            self.logger.log_operation("转存NC", status)
            
            return has_output
        
        except Exception as e:
            traceback.print_exc()
            return False


    def _process_single_grib_file_parallel(self, grib_file, ref_time):
        """并行处理单个GRIB文件"""
        try:
            time_info = self._parse_time_from_filename(grib_file)
            forecast_hour = time_info['forecast_hours']
            file_type = time_info['file_type']
            
            surface_ds, upper_ds = self._process_single_grib_file(grib_file, file_type)
            
            if surface_ds is not None:
                surface_ds.coords['forecast_time'] = ref_time + timedelta(hours=forecast_hour)
            
            if upper_ds is not None:
                upper_ds.coords['forecast_time'] = ref_time + timedelta(hours=forecast_hour)
            
            return surface_ds, upper_ds, forecast_hour
        except Exception as e:
            print(f"处理文件 {grib_file} 时出错: {str(e)}")
            traceback.print_exc()
            return None, None, None
        finally:
            self._clean_index_files(grib_file)

    def process_time_range(self, source_dir, grib_dir, output_dir, time_range_str):
        """优化后的时间段处理"""
        try:
            start_str, end_str = time_range_str.split('-')
            start_time = datetime.strptime(start_str, "%Y%m%d%H%M")
            end_time = datetime.strptime(end_str, "%Y%m%d%H%M")
            
            # 记录头部信息
            self.logger.log_header(source_dir, grib_dir, output_dir, time_range_str)
            
            current_time = start_time
            while current_time <= end_time:
                reference_time = current_time.strftime("%Y%m%d%H%M")
                year_str = current_time.strftime("%Y")
                ref_subdir = current_time.strftime("%Y%m%d%H")
                
                print(f"\n==== 处理时间点: {reference_time} ====")
                if self.logger.log_file:
                    with open(self.logger.log_file, 'a', encoding='utf-8') as f:
                        f.write(f"\n==== 处理时间点: {reference_time} ====\n")
                
                # 构建正确的源子目录路径（包含年份子目录）
                source_subdir_path = os.path.join(source_dir, year_str, ref_subdir)
                if not os.path.exists(source_subdir_path):
                    print(f"警告: 源子目录 {source_subdir_path} 不存在")
                    if self.logger.log_file:
                        with open(self.logger.log_file, 'a', encoding='utf-8') as f:
                            f.write(f"警告: 源子目录 {source_subdir_path} 不存在\n")
                    current_time += timedelta(hours=12)
                    continue
                
                # 1. 解压文件
                decompress_success = self.decompress_files(source_subdir_path, grib_dir, reference_time)
                if not decompress_success:
                    current_time += timedelta(hours=12)
                    continue
                
                # 2. 重命名文件
                rename_success = self.rename_files(grib_dir, reference_time)
                if not rename_success:
                    pass  # 即使重命名失败也继续处理
                
                # 3. 转换文件
                convert_success = self.convert_grib_to_nc(grib_dir, output_dir, reference_time)
                if not convert_success:
                    pass  # 即使转换失败也继续处理
                
                # 4. 清理临时文件
                try:
                    self._clean_directory(grib_dir)
                except Exception as e:
                    traceback.print_exc()
                
                current_time += timedelta(hours=12)
            
            self.logger.log_footer(True)
            return True
        except Exception as e:
            self.logger.log_footer(False)
            traceback.print_exc()
            return False


    # ------------------------- 内部方法 -------------------------
    
    def _generate_new_name(self, filepath, reference_time):
        """生成新文件名（修复日期解析版本）"""
        filename = Path(filepath).name
        
        # 尝试匹配TYPE6的特殊1分钟文件
        type6_match = re.match(r'W_NAFP_C_ECMF_\d+_P_C1D(\d{8})(\d{8})1', filename)
        if type6_match:
            ref_mdhm = type6_match.group(1)  # MMDDHHMM
            fcst_mdhm = type6_match.group(2) # MMDDHHMM
            
            # 解析日期时间（添加年份）
            ref_year = int(reference_time[:4])
            try:
                ref_dt = datetime.strptime(f"{ref_year}{ref_mdhm}", "%Y%m%d%H%M")
                fcst_dt = datetime.strptime(f"{ref_year}{fcst_mdhm}", "%Y%m%d%H%M")
                
                # 检查是否为TYPE6文件（1分钟差）
                if (fcst_dt - ref_dt) == timedelta(minutes=1):
                    extra_info = filename.split('_')[3]  # 获取原文件名中的额外信息
                    new_name = (
                        f"W_NAFP_C_ECMF_P_C1D_"
                        f"{ref_dt.strftime('%Y%m%d%H%M')}00_"
                        f"{fcst_dt.strftime('%Y%m%d%H%M')}001"
                        f"_{extra_info}"
                        f"{Path(filepath).suffix}"
                    )
                    return new_name
            except ValueError:
                pass
        
        # 处理常规文件
        match = re.match(r'W_NAFP_C_ECMF_\d+_P_C1D(\d{8})(\d{8})1', filename)
        if not match:
            return None
        
        # 获取原文件名中的额外信息部分
        extra_info = filename.split('_')[3]
        
        ref_mdhm = match.group(1)  # MMDDHHMM
        fcst_mdhm = match.group(2) # MMDDHHMM
        
        # 添加年份信息
        ref_year = int(reference_time[:4])
        try:
            ref_dt = datetime.strptime(f"{ref_year}{ref_mdhm}", "%Y%m%d%H%M")
            fcst_dt = datetime.strptime(f"{ref_year}{fcst_mdhm}", "%Y%m%d%H%M")
        except ValueError as e:
            print(f"日期解析错误: {e} | 文件名: {filename}")
            return None
        
        new_name = (
            f"W_NAFP_C_ECMF_P_C1D_"
            f"{ref_dt.strftime('%Y%m%d%H%M')}00_"
            f"{fcst_dt.strftime('%Y%m%d%H%M')}001"
            f"_{extra_info}"
            f"{Path(filepath).suffix}"
        )
        return new_name


    def _classify_grib_files(self, directory, reference_time):
        """分类GRIB文件"""
        ref_dt = datetime.strptime(reference_time, "%Y%m%d%H%M")
        
        # 第一类文件
        type1_patterns = [
            f"W_NAFP_C_ECMF_P_C1D_{reference_time}00_"
            f"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*"
            for h in self.TYPE1_FORECAST_HOURS
        ]
        
        # 第二类文件
        type2_patterns = [
            f"W_NAFP_C_ECMF_P_C1D_{reference_time}00_"
            f"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*"
            for h in self.TYPE2_FORECAST_HOURS
        ]
        
        # 第三类文件
        type3_patterns = [
            f"W_NAFP_C_ECMF_P_C1D_{reference_time}00_"
            f"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*"
            for h in self.TYPE3_FORECAST_HOURS
        ]
        
        # 第四类文件
        type4_patterns = [
            f"W_NAFP_C_ECMF_P_C1D_{reference_time}00_"
            f"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*"
            for h in self.TYPE4_FORECAST_HOURS
        ]
        
        # 第五类文件
        type5_patterns = [
            f"W_NAFP_C_ECMF_P_C1D_{reference_time}00_"
            f"{(ref_dt + timedelta(hours=h)).strftime('%Y%m%d%H%M')}001_*"
            for h in self.TYPE5_FORECAST_HOURS
        ]
        
        # 第六类文件 (特殊1分钟文件)
        type6_patterns = [
            f"W_NAFP_C_ECMF_P_C1D_{reference_time}00_"
            f"{(ref_dt + timedelta(minutes=1)).strftime('%Y%m%d%H%M')}001_*"
        ]
        
        type1_files = self._find_matching_files(directory, type1_patterns)
        type2_files = self._find_matching_files(directory, type2_patterns)
        type3_files = self._find_matching_files(directory, type3_patterns)
        type4_files = self._find_matching_files(directory, type4_patterns)
        type5_files = self._find_matching_files(directory, type5_patterns)
        type6_files = self._find_matching_files(directory, type6_patterns)
        
        return type1_files, type2_files, type3_files, type4_files, type5_files, type6_files

    def _find_matching_files(self, directory, patterns):
        """查找匹配模式的文件"""
        matched = []
        for p in patterns:
            found_files = glob.glob(os.path.join(directory, p))
            matched.extend(found_files)
        return sorted(matched)

    def _process_single_grib_file(self, grib_file, file_type):
        """处理单个GRIB文件"""
        # 检测可用气压层
        pressure_levels = set()
        for edition in [1, 2]:
            try:
                with xr.open_dataset(grib_file, engine="cfgrib", 
                                    backend_kwargs={'filter_by_keys': {'edition': edition}}) as ds:
                    if 'isobaricInhPa' in ds.coords:
                        pressure_levels.update(ds.isobaricInhPa.values)
            except Exception:
                continue
        
        # 根据文件类型确定需要提取的地面变量
        if file_type == 1:
            surface_vars = self.TYPE1_SURFACE_VARS
        elif file_type == 2:
            surface_vars = self.TYPE2_SURFACE_VARS
        elif file_type == 3:
            surface_vars = self.TYPE3_SURFACE_VARS
        elif file_type == 4:
            surface_vars = self.TYPE4_SURFACE_VARS
        elif file_type == 5:
            surface_vars = self.TYPE5_SURFACE_VARS
        elif file_type == 6:
            surface_vars = self.TYPE6_SURFACE_VARS
        else:
            surface_vars = []  # 未知类型的文件不提取地面变量
        
        # 并行读取数据
        surface_ds = []
        upper_ds = []
        
        for edition in [1, 2]:
            # 地面数据
            try:
                ds = xr.open_dataset(
                    grib_file,
                    engine="cfgrib",
                    backend_kwargs={
                        'filter_by_keys': {
                            'edition': edition,
                            'typeOfLevel': ['surface', 'meanSea', 'heightAboveGround', 
                            'heightAboveGroundLayer', 'cloudBase', 'cloudTop']
                        }
                    }
                )
                if ds.data_vars:
                    # 只保留当前类型需要的地面变量
                    vars_to_keep = list(ds.data_vars.keys())
                    if vars_to_keep:
                        ds = ds[vars_to_keep].expand_dims({'level': [0]})
                        surface_ds.append(ds)
            except Exception as e:
                continue
            
            # 高空数据 - 处理所有预报时次
            for level in sorted(pressure_levels, reverse=True):
                try:
                    ds = xr.open_dataset(
                        grib_file,
                        engine="cfgrib",
                        backend_kwargs={
                            'filter_by_keys': {
                                'edition': edition,
                                'typeOfLevel': 'isobaricInhPa',
                                'level': level
                            }
                        }
                    )
                    if ds.data_vars:
                        # 保留所有高空变量
                        vars_to_keep = [var for var in ds.data_vars if var in self.UPPER_VARS]
                        if vars_to_keep:
                            ds = ds[vars_to_keep].expand_dims({'isobaricInhPa': [level]})
                            upper_ds.append(ds)
                except Exception:
                    continue
        
        # 合并数据
        combined_surface = xr.merge(surface_ds) if surface_ds else None
        combined_upper = xr.concat(upper_ds, dim='isobaricInhPa') if upper_ds else None
        
        # 只对映射表中的变量进行重命名，其他变量保持不变
        if combined_surface is not None:
            rename_dict = {}
            for var in combined_surface.data_vars:
                if var in self.GRIB_TO_NC_VAR_MAP:
                    rename_dict[var] = self.GRIB_TO_NC_VAR_MAP[var]
            if rename_dict:
                combined_surface = combined_surface.rename(rename_dict)
        
        if combined_upper is not None:
            rename_dict = {}
            for var in combined_upper.data_vars:
                if var in self.GRIB_TO_NC_VAR_MAP:
                    rename_dict[var] = self.GRIB_TO_NC_VAR_MAP[var]
            if rename_dict:
                combined_upper = combined_upper.rename(rename_dict)
        
        return combined_surface, combined_upper

    def _parse_time_from_filename(self, filename):
        """从文件名解析时间信息（修复日期解析版本）"""
        filename = Path(filename).stem
        
        # 新文件名格式: W_NAFP_C_ECMF_P_C1D_YYYYMMDDHHMM00_YYYYMMDDHHMM001_*
        new_pattern = re.compile(
            r'W_NAFP_C_ECMF_P_C1D_'
            r'(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})00_'  # 参考时间
            r'(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})001'  # 预报时间
        )
        
        match = new_pattern.search(filename)
        if match:
            # 从新格式文件名中提取时间
            ref_year = int(match.group(1))
            ref_month = int(match.group(2))
            ref_day = int(match.group(3))
            ref_hour = int(match.group(4))
            ref_minute = int(match.group(5))
            
            fcst_year = int(match.group(6))
            fcst_month = int(match.group(7))
            fcst_day = int(match.group(8))
            fcst_hour = int(match.group(9))
            fcst_minute = int(match.group(10))
            
            ref_time = datetime(ref_year, ref_month, ref_day, ref_hour, ref_minute)
            fcst_time = datetime(fcst_year, fcst_month, fcst_day, fcst_hour, fcst_minute)
            
            time_diff = fcst_time - ref_time
            
            # 确定文件类型
            file_type = self._determine_file_type(time_diff)
            
            return {
                'reference_time': ref_time,
                'forecast_time': fcst_time,
                'forecast_hours': int(time_diff.total_seconds() / 3600),
                'file_type': file_type
            }
        
        # 旧文件名格式: W_NAFP_C_ECMF_*_P_C1DMMDDHHMMMMDDHHMM1
        old_pattern = re.compile(r'W_NAFP_C_ECMF_\d+_P_C1D(\d{8})(\d{8})1')
        match = old_pattern.search(filename)
        if match:
            ref_mdhm = match.group(1)  # MMDDHHMM
            fcst_mdhm = match.group(2) # MMDDHHMM
            
            # 从reference_time获取年份
            ref_year = int(filename.split('_')[5][:4]) if '_' in filename else int(datetime.now().year)
            
            try:
                ref_dt = datetime.strptime(f"{ref_year}{ref_mdhm}", "%Y%m%d%H%M")
                fcst_dt = datetime.strptime(f"{ref_year}{fcst_mdhm}", "%Y%m%d%H%M")
                
                time_diff = fcst_dt - ref_dt
                file_type = self._determine_file_type(time_diff)
                
                return {
                    'reference_time': ref_dt,
                    'forecast_time': fcst_dt,
                    'forecast_hours': int(time_diff.total_seconds() / 3600),
                    'file_type': file_type
                }
            except ValueError as e:
                print(f"日期解析错误: {e} | 文件名: {filename}")
                raise ValueError(f"无法从文件名 {filename} 中解析时间信息")
    
        raise ValueError(f"无法识别文件名格式: {filename}")
    
    def _get_grib_metadata(self, grib_file):
        """从GRIB文件中提取变量元数据"""
        metadata = {}
        try:
            with pygrib.open(grib_file) as grbs:
                for grb in grbs:
                    short_name = grb.shortName
                    # 双向检查映射关系
                    if short_name in self.GRIB_TO_NC_VAR_MAP:
                        nc_name = self.GRIB_TO_NC_VAR_MAP[short_name]
                    elif short_name in self.GRIB_TO_NC_VAR_MAP.values():
                        nc_name = short_name
                    else:
                        continue
                    
                    if nc_name not in metadata:
                        metadata[nc_name] = {
                            'long_name': grb.name,
                            'units': grb.units
                        }
        except Exception:
            pass
        return metadata

    def _clean_index_files(self, file_path):
        """清理临时索引文件"""
        try:
            idx_file = Path(file_path).with_suffix('.idx')
            if idx_file.exists():
                idx_file.unlink()
        except Exception:
            pass

    def _clean_directory(self, directory):
        """清理目录中的所有索引文件"""
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.idx'):
                    try:
                        os.remove(os.path.join(root, file))
                    except Exception:
                        pass

    def _save_combined_data(self, datasets, output_path, data_type, reference_time, metadata):
        """保存合并后的数据"""
        if not datasets:
            return
        
        try:
            # 修改这里：添加 compat='override' 参数来处理变量冲突
            combined = xr.concat([xr.merge(ds_list, compat='override') for ds_list in datasets], 
                               dim='forecast_time')
            
            ref_time_ns = np.datetime64(reference_time, 'ns')
            combined.coords['reference_time'] = ref_time_ns
            
            # 添加变量元数据
            for var in combined.data_vars:
                if var in metadata:
                    combined[var].attrs.update({
                        'long_name': metadata[var]['long_name'],
                        'units': metadata[var]['units']
                    })
            
            # 添加自定义全局属性
            combined.attrs.update({
                'modename': 'NAFP_ECMF_C1D',
                'description': 'Data sourced from GXQXJ'
            })
            
            # 设置编码
            encoding = {
                var: {
                    'zlib': True,
                    'complevel': 1, 
                    'dtype': 'float32',
                    '_FillValue': None,
                    'chunksizes': self._get_chunksizes(combined[var])
                } for var in combined.data_vars
            }
            encoding.update({
                'forecast_time': {'dtype': 'int32', '_FillValue': None},
                'reference_time': {'dtype': 'int64', '_FillValue': None}
            })
            
            combined.to_netcdf(output_path, encoding=encoding)
        except Exception as e:
            raise Exception(f"保存数据失败: {str(e)}")
            
    def _determine_file_type(self, time_diff):
        """根据时间差确定文件类型"""
        hours = time_diff.total_seconds() / 3600
        
        if time_diff == timedelta(minutes=1):
            return 6  # 特殊1分钟文件
        elif hours in self.TYPE1_FORECAST_HOURS:
            return 1
        elif hours in self.TYPE2_FORECAST_HOURS:
            return 2
        elif hours in self.TYPE3_FORECAST_HOURS:
            return 3
        elif hours in self.TYPE4_FORECAST_HOURS:
            return 4
        elif hours in self.TYPE5_FORECAST_HOURS:
            return 5
        else:
            return 0  # 未知类型

    def _get_chunksizes(self, var):
        """计算变量的合适chunk大小"""
        chunks = []
        for dim in var.dims:
            size = len(var[dim])
            if dim == 'isobaricInhPa':
                chunks.append(min(4, size))
            elif dim == 'latitude':
                chunks.append(min(128, size))
            elif dim == 'longitude':
                chunks.append(min(256, size))
            elif dim == 'forecast_time':
                chunks.append(1)
            else:
                chunks.append(size)
        return tuple(chunks)

if __name__ == "__main__":
    # ===== 用户配置区域 =====
    # 配置处理参数（修改这些变量即可）
    SOURCE_DIR = "/workspace/data/NAFP/EC_C1D"       # 原始.bz2文件目录
    GRIB_DIR = "/workspace/EC/TEMP/EC_C1D/EC_C1D_grib_202402191200"    # 解压后的GRIB文件目录
    OUTPUT_DIR = "/workspace/EC/TEMP/EC_C1D/EC_C1D_nc"    # 输出NetCDF文件目录
    TIME_RANGE = "202402191200-202402291200"             # 处理时间范围 201810111200
    LOG_FILE = os.path.join(OUTPUT_DIR, "processing_log202402191200-202402291200.txt")  # 日志文件路径

    # ===== 执行处理 =====
    processor = ECMWFProcessor(LOG_FILE)
    
    # 确保目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(GRIB_DIR, exist_ok=True)
    
    # 创建处理器实例并运行
    success = processor.process_time_range(SOURCE_DIR, GRIB_DIR, OUTPUT_DIR, TIME_RANGE)
    
    if not success:
        sys.exit(1)


