#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于EC_2022_new.ipynb的最小化修改版本
只修改必要的部分以支持新的GRIB2格式: Z_NAFP_C_BABJ_*_HRCLDAS_*.GRB2
保持原有的所有处理逻辑和结构不变
"""

import os
import re
import glob
import warnings
import traceback
from datetime import datetime, timedelta
from collections import defaultdict
import xarray as xr
import numpy as np
import logging
import pygrib
import time
from multiprocessing import cpu_count
warnings.filterwarnings("ignore", category=FutureWarning)

# 配置日志和警告 (保持原有配置)
logging.getLogger('cfgrib').setLevel(logging.ERROR)
logging.getLogger('eccodes').setLevel(logging.ERROR)
logging.getLogger('cfgrib').setLevel(logging.CRITICAL)
warnings.filterwarnings("ignore", category=RuntimeWarning, module="cfgrib")

class TimestampedLogger:
    """带时间戳的日志记录器(保持原有实现)"""
    def __init__(self, log_file=None):
        self.log_file = log_file
        if log_file:
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("")
        self.operation_start_time = {}

    def log_header(self, source_dir, grib_dir, output_dir, time_range):
        """记录头部信息"""
        message = f"""===== GRIB数据处理程序 (适配版) =====
源目录: {source_dir}
GRIB目录: {grib_dir}
输出目录: {output_dir}
时间范围: {time_range}
日志文件: {self.log_file if self.log_file else '无'}
{"=" * 40}

"""
        print(message)
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message)

    def start_operation(self, operation_name):
        """记录操作开始时间"""
        self.operation_start_time[operation_name] = time.time()

    def log_operation(self, operation_name, status_message):
        """记录操作结果"""
        if operation_name not in self.operation_start_time:
            self.start_operation(operation_name)
        
        elapsed = time.time() - self.operation_start_time[operation_name]
        
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
        message = f"{timestamp} {operation_name.ljust(12)}{elapsed:.2f}s\t{status_message}"
        
        print(message)
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message + "\n")
        
        if operation_name in self.operation_start_time:
            del self.operation_start_time[operation_name]

    def log_footer(self, success=True):
        """记录结束信息"""
        message = "\n===== 处理完成 ====="
        if not success:
            message += "\n===== 处理过程中出现错误 ====="
        print(message)
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message + "\n")

class ECMWFProcessorAdapted:
    """适配新GRIB2格式的ECMWF处理器 - 保持原有逻辑结构"""

    def __init__(self, log_file=None):
        # 初始化日志记录器 (保持原有)
        self.logger = TimestampedLogger(log_file)
        
        # 保持原有的变量定义结构，但添加新格式的变量
        # 原有的地面变量分类 (保持不变)
        self.TYPE1_SURFACE_VARS = [
            '100u', '100v', '10fg3', '10fg6', '10u', '10v', '2d', '2t', 'cape', 'capes', 
            'cp', 'deg0l', 'fal', 'fzra', 'lcc', 'lsp', 'mn2t3', 'mn2t6', 'msl',
            'mx2t3', 'mx2t6', 'ptype', 'rsn', 'sd', 'sf', 'skt', 'sp', 'sst', 'tcc',
            'tcw', 'tcwv', 'tp', 'vis'
        ]
        
        # 添加新格式的常见变量 (扩展原有分类)
        self.NEW_FORMAT_SURFACE_VARS = [
            '2r',    # 2米相对湿度
            '2t',    # 2米温度
            '2d',    # 2米露点温度
            'sp',    # 地面气压
            '10u',   # 10米U风
            '10v',   # 10米V风
            'tp',    # 总降水
            'msl',   # 海平面气压
            'skt',   # 地表温度
            'q',     # 比湿
            'sh',    # 比湿 (另一种表示)
            'hus',   # 比湿 (标准名称)
        ]
        
        # 合并所有地面变量
        self.ALL_SURFACE_VARS = list(set(self.TYPE1_SURFACE_VARS + self.NEW_FORMAT_SURFACE_VARS))
        
        # 保持原有的高空变量定义
        self.UPPER_VARS = ['v', 'u', 'd', 't', 'q', 'gh', 'pv', 'r', 'w']
        
        # 扩展原有的变量名映射，添加新格式变量
        self.GRIB_TO_NC_VAR_MAP = {
            # 原有映射 (保持不变)
            '100u': 'u100', '100v': 'v100', '10u': 'u10', '10v': 'v10',
            '10fg3': 'fg310', '10fg6': 'p10fg6', '2d': 'd2m', '2t': 't2m',
            'cape': 'cape', 'capes': 'capes', 'cp': 'cp', 'deg0l': 'deg0l',
            'fal': 'fal', 'fzra': 'fzra', 'lcc': 'lcc', 'lsp': 'lsp',
            'mn2t3': 'mn2t3', 'mn2t6': 'mn2t6', 'msl': 'msl', 'mx2t3': 'mx2t3',
            'mx2t6': 'mx2t6', 'ptype': 'ptype', 'rsn': 'rsn', 'sd': 'sd',
            'sf': 'sf', 'skt': 'skt', 'sp': 'sp', 'sst': 'sst', 'tcc': 'tcc',
            'tcw': 'tcw', 'tcwv': 'tcwv', 'tp': 'tp', 'vis': 'vis',
            'al': 'al', 'z': 'z',
            # 新格式变量映射
            '2r': 'rh2m',    # 2米相对湿度
            'q': 'q2m',      # 2米比湿
            'sh': 'sh2m',    # 2米比湿 (另一种表示)
            'hus': 'hus2m',  # 2米比湿 (标准名称)
        }
        
        # 保持原有的预报时效分类 (但新格式可能不需要)
        self.TYPE1_FORECAST_HOURS = [
            6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 66, 72, 78, 84, 90, 96, 
            102, 108, 114, 120, 126, 132, 138, 144
        ]
        
        # 新格式通常是单时次文件，添加0时效支持
        self.NEW_FORMAT_FORECAST_HOURS = [0]  # 实况或分析场
        
        # 保持原有的其他配置
        self.metadata_cache = {}
        self.MAX_WORKERS = min(32, max(1, cpu_count() - 2))

    def process_new_format_file(self, input_file, output_dir, reference_time=None):
        """处理新格式的单个GRIB2文件 - 保持原有转换逻辑"""
        self.logger.start_operation("处理新格式")
        
        try:
            # 如果没有提供参考时间，从文件名推断
            if reference_time is None:
                reference_time = self._parse_new_format_time(input_file)
            
            # 使用原有的数据读取和转换逻辑
            surface_ds, upper_ds = self._process_single_grib_file_new_format(input_file)
            
            if surface_ds is None and upper_ds is None:
                self.logger.log_operation("处理新格式", "未提取到任何数据")
                return False
            
            # 保持原有的保存逻辑
            ref_dt = datetime.strptime(reference_time, "%Y%m%d%H%M") if isinstance(reference_time, str) else reference_time
            output_prefix = f"HRCLDAS_GRIB_{ref_dt.strftime('%Y%m%d%H%M')}"
            
            os.makedirs(output_dir, exist_ok=True)
            saved_files = []
            
            # 保存地面数据 (保持原有格式)
            if surface_ds is not None:
                surface_path = os.path.join(output_dir, f"{output_prefix}_SURF.NC")
                self._save_dataset_original_format(surface_ds, surface_path, ref_dt)
                saved_files.append(f"SURF.NC -> {surface_path}")
            
            # 保存高空数据 (保持原有格式)
            if upper_ds is not None:
                upper_path = os.path.join(output_dir, f"{output_prefix}_UPAR.NC")
                self._save_dataset_original_format(upper_ds, upper_path, ref_dt)
                saved_files.append(f"UPAR.NC -> {upper_path}")
            
            if saved_files:
                status = " | ".join(saved_files)
                self.logger.log_operation("处理新格式", status)
                return True
            else:
                self.logger.log_operation("处理新格式", "没有数据可保存")
                return False

        except Exception as e:
            self.logger.log_operation("处理新格式", f"失败: {str(e)}")
            traceback.print_exc()
            return False

    def _add_coordinate_attributes(self, dataset):
        """为数据集添加完整的坐标属性"""
        # 为经纬度坐标添加标准属性
        if 'latitude' in dataset.coords:
            dataset.coords['latitude'].attrs.update({
                'units': 'degrees_north',
                'long_name': 'latitude',
                'standard_name': 'latitude',
                'axis': 'Y'
            })

        if 'longitude' in dataset.coords:
            dataset.coords['longitude'].attrs.update({
                'units': 'degrees_east',
                'long_name': 'longitude',
                'standard_name': 'longitude',
                'axis': 'X'
            })

        # 为气压层坐标添加属性
        if 'isobaricInhPa' in dataset.coords:
            dataset.coords['isobaricInhPa'].attrs.update({
                'units': 'hPa',
                'long_name': 'pressure',
                'standard_name': 'air_pressure',
                'axis': 'Z',
                'positive': 'down'
            })

        # 为时间坐标添加属性
        if 'time' in dataset.coords:
            dataset.coords['time'].attrs.update({
                'long_name': 'time',
                'standard_name': 'time',
                'axis': 'T'
            })

        # 为level坐标添加属性 (地面数据)
        if 'level' in dataset.coords:
            dataset.coords['level'].attrs.update({
                'long_name': 'level',
                'units': '1'
            })

    def _parse_new_format_time(self, file_path):
        """解析新格式文件名中的时间信息"""
        filename = os.path.basename(file_path)

        # 新格式: Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
        pattern = r'Z_\w+_C_\w+_(\d{14})_P_\w+_\w+_\w+_\w+_\w+-\w+-(\d{10})\.GRB2?'
        match = re.search(pattern, filename)

        if match:
            data_time_str = match.group(2)  # YYYYMMDDHH
            try:
                data_time = datetime.strptime(data_time_str, "%Y%m%d%H")
                return data_time.strftime("%Y%m%d%H%M")  # 转换为原有格式
            except ValueError:
                pass

        # 如果解析失败，使用当前时间
        return datetime.now().strftime("%Y%m%d%H%M")

    def _process_single_grib_file_new_format(self, grib_file):
        """处理新格式的单个GRIB文件 - 保持原有数据读取逻辑"""
        try:
            # 尝试使用cfgrib读取 (保持原有逻辑)
            surface_ds = None
            upper_ds = None

            # 读取地面数据
            try:
                surface_ds = xr.open_dataset(
                    grib_file,
                    engine="cfgrib",
                    backend_kwargs={
                        'filter_by_keys': {
                            'typeOfLevel': ['surface', 'heightAboveGround', 'meanSea']
                        }
                    }
                )

                # 保留所有地面变量，不进行过滤
                if surface_ds.data_vars:
                    print(f"找到地面变量: {list(surface_ds.data_vars.keys())}")
                else:
                    surface_ds = None

            except Exception as e:
                print(f"地面数据读取失败: {e}")
                surface_ds = None

            # 读取高空数据
            try:
                upper_ds = xr.open_dataset(
                    grib_file,
                    engine="cfgrib",
                    backend_kwargs={
                        'filter_by_keys': {
                            'typeOfLevel': 'isobaricInhPa'
                        }
                    }
                )

                # 保留所有高空变量，不进行过滤
                if upper_ds.data_vars:
                    print(f"找到高空变量: {list(upper_ds.data_vars.keys())}")
                else:
                    upper_ds = None

            except Exception as e:
                print(f"高空数据读取失败: {e}")
                upper_ds = None

            # 如果cfgrib失败，使用pygrib备用方案
            if surface_ds is None and upper_ds is None:
                surface_ds, upper_ds = self._read_with_pygrib_backup(grib_file)

            # 应用变量重命名 (保持原有逻辑)
            if surface_ds is not None:
                rename_dict = {}
                for var in surface_ds.data_vars:
                    if var in self.GRIB_TO_NC_VAR_MAP:
                        rename_dict[var] = self.GRIB_TO_NC_VAR_MAP[var]
                if rename_dict:
                    surface_ds = surface_ds.rename(rename_dict)
                # 添加坐标属性
                self._add_coordinate_attributes(surface_ds)

            if upper_ds is not None:
                rename_dict = {}
                for var in upper_ds.data_vars:
                    if var in self.GRIB_TO_NC_VAR_MAP:
                        rename_dict[var] = self.GRIB_TO_NC_VAR_MAP[var]
                if rename_dict:
                    upper_ds = upper_ds.rename(rename_dict)
                # 添加坐标属性
                self._add_coordinate_attributes(upper_ds)

            return surface_ds, upper_ds

        except Exception as e:
            print(f"处理GRIB文件失败: {e}")
            return None, None

    def _read_with_pygrib_backup(self, grib_file):
        """使用pygrib的备用读取方案"""
        try:
            with pygrib.open(grib_file) as grbs:
                messages = list(grbs)

                if not messages:
                    return None, None

                # 获取网格信息
                first_msg = messages[0]
                lats, lons = first_msg.latlons()
                lat_1d = lats[:, 0]
                lon_1d = lons[0, :]

                # 分类数据
                surface_data = {}
                upper_data = defaultdict(dict)

                for msg in messages:
                    var_name = msg.shortName
                    level_type = msg.typeOfLevel
                    level_value = msg.level
                    data = msg.values

                    # 创建DataArray with proper coordinate attributes
                    da = xr.DataArray(
                        data,
                        dims=['latitude', 'longitude'],
                        coords={
                            'latitude': xr.DataArray(
                                lat_1d,
                                dims=['latitude'],
                                attrs={
                                    'units': 'degrees_north',
                                    'long_name': 'latitude',
                                    'standard_name': 'latitude'
                                }
                            ),
                            'longitude': xr.DataArray(
                                lon_1d,
                                dims=['longitude'],
                                attrs={
                                    'units': 'degrees_east',
                                    'long_name': 'longitude',
                                    'standard_name': 'longitude'
                                }
                            )
                        },
                        attrs={
                            'long_name': msg.name,
                            'units': msg.units,
                            'level_type': level_type,
                            'level': level_value
                        }
                    )

                    # 分类存储 - 不过滤变量，保留所有变量
                    if level_type in ['surface', 'heightAboveGround', 'meanSea']:
                        surface_data[var_name] = da
                        print(f"添加地面变量: {var_name}")
                    elif level_type == 'isobaricInhPa':
                        upper_data[level_value][var_name] = da
                        print(f"添加高空变量: {var_name} (层次: {level_value})")

                # 创建Dataset
                surface_ds = None
                upper_ds = None

                if surface_data:
                    surface_ds = xr.Dataset(surface_data)
                    # 添加level维度以保持一致性
                    surface_ds = surface_ds.expand_dims({'level': [0]})

                if upper_data:
                    # 按气压层组织高空数据
                    upper_datasets = []
                    for level in sorted(upper_data.keys(), reverse=True):
                        level_ds = xr.Dataset(upper_data[level])
                        level_ds = level_ds.expand_dims({'isobaricInhPa': [level]})
                        upper_datasets.append(level_ds)

                    if upper_datasets:
                        upper_ds = xr.concat(upper_datasets, dim='isobaricInhPa')

                return surface_ds, upper_ds

        except Exception as e:
            print(f"pygrib备用读取失败: {e}")
            return None, None

    def _save_dataset_original_format(self, dataset, output_path, ref_time):
        """保存数据集 - 完全保持原有格式和逻辑，增加地理坐标系信息"""
        try:
            # 添加时间坐标 (保持原有逻辑)
            if 'time' not in dataset.coords:
                dataset = dataset.expand_dims('time')
                dataset.coords['time'] = [ref_time]

            # 添加参考时间 (保持原有逻辑)
            dataset.coords['reference_time'] = np.datetime64(ref_time, 'ns')

            # 确保坐标有正确的属性
            self._add_coordinate_attributes(dataset)

            # 添加全局属性 (保持原有逻辑 + 地理信息)
            dataset.attrs.update({
                'modename': 'HRCLDAS_ADAPTED',
                'description': 'Adapted HRCLDAS data processed with original ECMWF logic',
                'creation_time': datetime.now().isoformat(),
                'source_file': 'HRCLDAS GRIB2 file',
                # 添加地理坐标系信息
                'Conventions': 'CF-1.6',
                'coordinate_system': 'WGS84',
                'geospatial_lat_min': float(dataset.latitude.min()),
                'geospatial_lat_max': float(dataset.latitude.max()),
                'geospatial_lon_min': float(dataset.longitude.min()),
                'geospatial_lon_max': float(dataset.longitude.max()),
                'geospatial_lat_units': 'degrees_north',
                'geospatial_lon_units': 'degrees_east'
            })

            # 设置编码 (完全保持原有逻辑)
            encoding = {}
            for var in dataset.data_vars:
                encoding[var] = {
                    'zlib': True,
                    'complevel': 1,
                    'dtype': 'float32',
                    '_FillValue': None,
                    'chunksizes': self._get_chunksizes_original(dataset[var])
                }

            # 坐标编码 (保持原有逻辑)
            encoding.update({
                'time': {'dtype': 'int64', '_FillValue': None},
                'reference_time': {'dtype': 'int64', '_FillValue': None}
            })

            # 保存文件
            dataset.to_netcdf(output_path, encoding=encoding)
            print(f"✓ 保存成功: {output_path}")

        except Exception as e:
            print(f"✗ 保存失败: {e}")
            raise

    def _get_chunksizes_original(self, var):
        """计算变量的合适chunk大小 - 保持原有逻辑"""
        chunks = []
        for dim in var.dims:
            size = len(var[dim])
            if dim == 'isobaricInhPa':
                chunks.append(min(4, size))
            elif dim == 'latitude':
                chunks.append(min(128, size))
            elif dim == 'longitude':
                chunks.append(min(256, size))
            elif dim in ['time', 'forecast_time']:
                chunks.append(1)
            else:
                chunks.append(size)
        return tuple(chunks)

    def batch_process_new_format_directory(self, input_dir, output_dir, pattern="*.GRB2"):
        """批量处理新格式文件目录 - 保持原有批量处理逻辑"""
        self.logger.start_operation("批量处理")

        print(f"批量处理新格式GRIB2文件...")
        print(f"输入目录: {input_dir}")
        print(f"输出目录: {output_dir}")
        print(f"文件模式: {pattern}")

        # 查找文件
        search_pattern = os.path.join(input_dir, pattern)
        files = glob.glob(search_pattern)

        if not files:
            self.logger.log_operation("批量处理", f"未找到匹配的文件: {search_pattern}")
            return False

        print(f"找到 {len(files)} 个文件")

        success_count = 0
        for i, file_path in enumerate(files, 1):
            print(f"\n--- 处理文件 {i}/{len(files)} ---")
            filename = os.path.basename(file_path)
            print(f"文件: {filename}")

            try:
                if self.process_new_format_file(file_path, output_dir):
                    success_count += 1
                    print("✓ 处理成功")
                else:
                    print("✗ 处理失败")

            except Exception as e:
                print(f"✗ 处理异常: {e}")
                continue

        status = f"成功处理 {success_count}/{len(files)} 个文件"
        self.logger.log_operation("批量处理", status)
        print(f"\n批量处理完成: {status}")
        return success_count > 0

    def process_time_series_new_format(self, input_dir, output_dir, start_time, end_time, time_step_hours=1):
        """处理新格式的时间序列文件 - 适配多时间变量"""
        self.logger.start_operation("时间序列处理")

        print(f"处理时间序列: {start_time} 到 {end_time}")

        start_dt = datetime.strptime(start_time, "%Y%m%d%H%M")
        end_dt = datetime.strptime(end_time, "%Y%m%d%H%M")

        current_time = start_dt
        success_count = 0
        total_count = 0

        # 收集所有时间点的数据
        all_surface_datasets = []
        all_upper_datasets = []

        while current_time <= end_dt:
            time_str = current_time.strftime("%Y%m%d%H")
            print(f"\n处理时间点: {time_str}")

            # 查找该时间点的文件
            pattern = f"*{time_str}*.GRB2"
            search_path = os.path.join(input_dir, pattern)
            time_files = glob.glob(search_path)

            if time_files:
                for file_path in time_files:
                    total_count += 1
                    try:
                        surface_ds, upper_ds = self._process_single_grib_file_new_format(file_path)

                        if surface_ds is not None:
                            # 添加时间坐标
                            surface_ds = surface_ds.expand_dims('time')
                            surface_ds.coords['time'] = [current_time]
                            all_surface_datasets.append(surface_ds)

                        if upper_ds is not None:
                            # 添加时间坐标
                            upper_ds = upper_ds.expand_dims('time')
                            upper_ds.coords['time'] = [current_time]
                            all_upper_datasets.append(upper_ds)

                        success_count += 1
                        print(f"✓ 成功处理: {os.path.basename(file_path)}")

                    except Exception as e:
                        print(f"✗ 处理失败: {os.path.basename(file_path)} - {e}")
            else:
                print(f"未找到时间点 {time_str} 的文件")

            current_time += timedelta(hours=time_step_hours)

        # 合并时间序列数据 (保持原有逻辑)
        os.makedirs(output_dir, exist_ok=True)

        if all_surface_datasets:
            try:
                combined_surface = xr.concat(all_surface_datasets, dim='time')
                surface_path = os.path.join(output_dir, f"HRCLDAS_TIMESERIES_{start_time}_{end_time}_SURF.NC")
                self._save_dataset_original_format(combined_surface, surface_path, start_dt)
                print(f"✓ 保存地面时间序列: {surface_path}")
            except Exception as e:
                print(f"✗ 地面时间序列合并失败: {e}")

        if all_upper_datasets:
            try:
                combined_upper = xr.concat(all_upper_datasets, dim='time')
                upper_path = os.path.join(output_dir, f"HRCLDAS_TIMESERIES_{start_time}_{end_time}_UPAR.NC")
                self._save_dataset_original_format(combined_upper, upper_path, start_dt)
                print(f"✓ 保存高空时间序列: {upper_path}")
            except Exception as e:
                print(f"✗ 高空时间序列合并失败: {e}")

        status = f"时间序列处理完成: {success_count}/{total_count} 个文件成功"
        self.logger.log_operation("时间序列处理", status)
        return success_count > 0

def main():
    """主函数 - 使用示例"""

    # ===== 配置参数 (根据你的实际情况修改) =====

    # 示例1: 处理单个新格式文件
    SINGLE_FILE = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    OUTPUT_DIR = "output_adapted"
    LOG_FILE = os.path.join(OUTPUT_DIR, "processing_adapted.log")

    # 示例2: 批量处理目录
    BATCH_INPUT_DIR = "."  # 当前目录
    BATCH_OUTPUT_DIR = "batch_output_adapted"

    # 示例3: 时间序列处理
    TIMESERIES_INPUT_DIR = "timeseries_input"
    TIMESERIES_OUTPUT_DIR = "timeseries_output_adapted"
    START_TIME = "202401010000"
    END_TIME = "202401010300"  # 处理4个小时的数据

    # ===== 创建处理器 =====
    processor = ECMWFProcessorAdapted(LOG_FILE)

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print("ECMWF处理器适配版 - 支持新GRIB2格式")
    print("=" * 50)

    # ===== 选择处理模式 =====

    # 模式1: 处理单个文件
    if os.path.exists(SINGLE_FILE):
        print(f"\n模式1: 处理单个文件")
        print(f"文件: {SINGLE_FILE}")

        try:
            success = processor.process_new_format_file(SINGLE_FILE, OUTPUT_DIR)
            if success:
                print("✓ 单文件处理成功")
            else:
                print("✗ 单文件处理失败")
        except Exception as e:
            print(f"✗ 单文件处理异常: {e}")

    # 模式2: 批量处理目录
    print(f"\n模式2: 批量处理目录")
    print(f"输入目录: {BATCH_INPUT_DIR}")
    print(f"输出目录: {BATCH_OUTPUT_DIR}")

    try:
        success = processor.batch_process_new_format_directory(
            BATCH_INPUT_DIR,
            BATCH_OUTPUT_DIR
        )
        if success:
            print("✓ 批量处理成功")
        else:
            print("✗ 批量处理失败")
    except Exception as e:
        print(f"✗ 批量处理异常: {e}")

    # 模式3: 时间序列处理 (如果有时间序列数据)
    if os.path.exists(TIMESERIES_INPUT_DIR):
        print(f"\n模式3: 时间序列处理")
        print(f"时间范围: {START_TIME} - {END_TIME}")

        try:
            success = processor.process_time_series_new_format(
                TIMESERIES_INPUT_DIR,
                TIMESERIES_OUTPUT_DIR,
                START_TIME,
                END_TIME
            )
            if success:
                print("✓ 时间序列处理成功")
            else:
                print("✗ 时间序列处理失败")
        except Exception as e:
            print(f"✗ 时间序列处理异常: {e}")

    print("\n" + "=" * 50)
    print("处理完成！")
    print(f"详细日志请查看: {LOG_FILE}")

if __name__ == "__main__":
    main()
