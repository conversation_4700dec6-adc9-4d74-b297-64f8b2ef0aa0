#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新GRIB2格式数据处理器
适配 Z_NAFP_C_BABJ_*_HRCLDAS_* 格式的GRIB2文件
"""

import os
import re
import glob
import xarray as xr
import numpy as np
import pygrib
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings("ignore")

class NewGRIB2Processor:
    """新GRIB2格式数据处理器"""
    
    def __init__(self):
        # 常见变量名映射 (根据实际数据调整)
        self.VARIABLE_MAPPING = {
            'QAIR': 'specific_humidity',    # 比湿
            'TEMP': 'temperature',          # 温度
            'PRES': 'pressure',            # 气压
            'UGRD': 'u_wind',              # U风分量
            'VGRD': 'v_wind',              # V风分量
            'RH': 'relative_humidity',      # 相对湿度
            'PRCP': 'precipitation',        # 降水
            # 根据需要添加更多变量映射
        }
        
        # 变量单位映射
        self.UNIT_MAPPING = {
            'QAIR': 'kg/kg',
            'TEMP': 'K',
            'PRES': 'Pa',
            'UGRD': 'm/s',
            'VGRD': 'm/s',
            'RH': '%',
            'PRCP': 'mm'
        }
    
    def parse_filename(self, filename):
        """解析新格式的GRIB2文件名"""
        # Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
        pattern = r'Z_(\w+)_C_(\w+)_(\d{14})_P_(\w+)_(\w+)_(\w+)_(\w+)_(\w+)-(\w+)-(\d{10})\.(\w+)'
        
        match = re.match(pattern, filename)
        if not match:
            raise ValueError(f"无法解析文件名: {filename}")
        
        groups = match.groups()
        
        try:
            # 解析时间信息
            creation_time = datetime.strptime(groups[2], "%Y%m%d%H%M%S")
            data_time = datetime.strptime(groups[9], "%Y%m%d%H")
        except ValueError as e:
            raise ValueError(f"时间解析错误: {e}")
        
        return {
            'data_source': groups[0],      # NAFP
            'center_code': groups[1],      # BABJ
            'creation_time': creation_time,
            'product_type': groups[3],     # HRCLDAS
            'data_type': groups[4],        # RT
            'region': groups[5],           # BENN
            'resolution': groups[6],       # 0P01
            'variable_info': groups[7],    # HOR
            'variable_name': groups[8],    # QAIR
            'data_time': data_time,
            'file_format': groups[10],     # GRB2
            'original_filename': filename
        }
    
    def analyze_single_file(self, file_path):
        """分析单个GRIB2文件"""
        print(f"\n分析文件: {os.path.basename(file_path)}")
        
        # 解析文件名
        try:
            file_info = self.parse_filename(os.path.basename(file_path))
            print(f"变量: {file_info['variable_name']}")
            print(f"数据时间: {file_info['data_time']}")
            print(f"分辨率: {file_info['resolution']}")
        except Exception as e:
            print(f"文件名解析失败: {e}")
            return None
        
        # 分析GRIB内容
        try:
            # 使用pygrib获取详细信息
            with pygrib.open(file_path) as grbs:
                messages = list(grbs)
                print(f"GRIB消息数: {len(messages)}")
                
                if messages:
                    grb = messages[0]  # 分析第一个消息
                    print(f"变量短名: {grb.shortName}")
                    print(f"变量长名: {grb.name}")
                    print(f"单位: {grb.units}")
                    print(f"层次类型: {grb.typeOfLevel}")
                    print(f"层次值: {grb.level}")
                    
                    if hasattr(grb, 'Ni') and hasattr(grb, 'Nj'):
                        print(f"网格大小: {grb.Ni} x {grb.Nj}")
                    
                    # 地理信息
                    if hasattr(grb, 'latitudeOfFirstGridPointInDegrees'):
                        print(f"纬度范围: {grb.latitudeOfFirstGridPointInDegrees:.3f} 到 {grb.latitudeOfLastGridPointInDegrees:.3f}")
                        print(f"经度范围: {grb.longitudeOfFirstGridPointInDegrees:.3f} 到 {grb.longitudeOfLastGridPointInDegrees:.3f}")
        
        except Exception as e:
            print(f"GRIB分析失败: {e}")
        
        # 使用xarray读取数据
        try:
            ds = xr.open_dataset(file_path, engine='cfgrib')
            print(f"xarray维度: {dict(ds.dims)}")
            print(f"数据变量: {list(ds.data_vars.keys())}")
            print(f"坐标: {list(ds.coords.keys())}")
            
            file_info['dataset'] = ds
            
        except Exception as e:
            print(f"xarray读取失败: {e}")
        
        return file_info
    
    def process_file_to_netcdf(self, file_path, output_dir):
        """将单个GRIB2文件转换为NetCDF"""
        try:
            # 分析文件
            file_info = self.analyze_single_file(file_path)
            if not file_info or 'dataset' not in file_info:
                print("文件分析失败，跳过处理")
                return False
            
            ds = file_info['dataset']
            
            # 生成输出文件名
            var_name = file_info['variable_name']
            data_time = file_info['data_time']
            output_filename = f"HRCLDAS_{var_name}_{data_time.strftime('%Y%m%d%H')}.nc"
            output_path = os.path.join(output_dir, output_filename)
            
            # 数据预处理
            processed_ds = self._preprocess_dataset(ds, file_info)
            
            # 保存为NetCDF
            os.makedirs(output_dir, exist_ok=True)
            
            # 设置编码参数
            encoding = {}
            for var in processed_ds.data_vars:
                encoding[var] = {
                    'zlib': True,
                    'complevel': 4,
                    'dtype': 'float32'
                }
            
            processed_ds.to_netcdf(output_path, encoding=encoding)
            print(f"成功保存: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"处理失败: {e}")
            return False
    
    def _preprocess_dataset(self, ds, file_info):
        """预处理数据集"""
        # 复制数据集
        processed_ds = ds.copy()
        
        # 添加时间坐标
        if 'time' not in processed_ds.coords:
            processed_ds = processed_ds.expand_dims('time')
            processed_ds.coords['time'] = [file_info['data_time']]
        
        # 重命名变量
        var_name = file_info['variable_name']
        if var_name in self.VARIABLE_MAPPING:
            old_vars = list(processed_ds.data_vars.keys())
            if old_vars:
                new_name = self.VARIABLE_MAPPING[var_name]
                processed_ds = processed_ds.rename({old_vars[0]: new_name})
        
        # 添加属性
        processed_ds.attrs.update({
            'title': f"HRCLDAS {file_info['variable_name']} Data",
            'source': f"NAFP-{file_info['center_code']}",
            'product_type': file_info['product_type'],
            'resolution': file_info['resolution'],
            'creation_time': file_info['creation_time'].isoformat(),
            'data_time': file_info['data_time'].isoformat()
        })
        
        # 为变量添加属性
        for var_name in processed_ds.data_vars:
            original_var = file_info['variable_name']
            if original_var in self.UNIT_MAPPING:
                processed_ds[var_name].attrs['units'] = self.UNIT_MAPPING[original_var]
            processed_ds[var_name].attrs['long_name'] = f"{original_var} from HRCLDAS"
        
        return processed_ds
    
    def batch_process_directory(self, input_dir, output_dir, pattern="*.GRB2"):
        """批量处理目录中的GRIB2文件"""
        print(f"批量处理目录: {input_dir}")
        print(f"输出目录: {output_dir}")
        
        # 查找匹配的文件
        search_pattern = os.path.join(input_dir, pattern)
        files = glob.glob(search_pattern)
        
        if not files:
            print(f"未找到匹配的文件: {search_pattern}")
            return
        
        print(f"找到 {len(files)} 个文件")
        
        success_count = 0
        for file_path in files:
            print(f"\n处理: {os.path.basename(file_path)}")
            if self.process_file_to_netcdf(file_path, output_dir):
                success_count += 1
        
        print(f"\n批量处理完成: {success_count}/{len(files)} 个文件成功处理")

def main():
    """主函数 - 使用示例"""
    processor = NewGRIB2Processor()
    
    # 示例1: 分析单个文件
    file_path = "Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2"
    
    if os.path.exists(file_path):
        print("=== 单文件分析 ===")
        processor.analyze_single_file(file_path)
        
        print("\n=== 转换为NetCDF ===")
        processor.process_file_to_netcdf(file_path, "output")
    else:
        print(f"文件不存在: {file_path}")
        print("请将GRIB2文件放在当前目录下，或修改文件路径")
    
    # 示例2: 批量处理目录
    # input_directory = "path/to/grib2/files"
    # output_directory = "path/to/output/netcdf"
    # processor.batch_process_directory(input_directory, output_directory)

if __name__ == "__main__":
    main()
